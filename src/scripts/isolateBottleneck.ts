/**
 * Isolate the performance bottleneck in bestWords function
 */

import { readFileSync } from 'node:fs';
import { join } from 'path';
import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';

// Profile a function
function profile<T>(name: string, fn: () => T): T {
	const start = performance.now();
	const result = fn();
	const end = performance.now();
	console.log(`⏱️  ${name}: ${(end - start).toFixed(2)}ms`);
	return result;
}

function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			board.setTile(row, col, tile);
		}
	}
	return board;
}

async function main() {
	console.log('🔍 Isolating Performance Bottleneck');
	console.log('===================================\n');

	// Test 1: Dictionary file loading
	console.log('1️⃣  Testing dictionary file loading:');
	const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
	
	let dictBytes: Buffer;
	profile('readFileSync', () => {
		dictBytes = readFileSync(DICT_BIN);
	});
	console.log(`   Dictionary size: ${(dictBytes!.length / 1024 / 1024).toFixed(2)} MB\n`);

	// Test 2: DataView creation
	console.log('2️⃣  Testing DataView creation:');
	let dv: DataView;
	profile('DataView creation', () => {
		dv = new DataView(dictBytes!.buffer, dictBytes!.byteOffset);
	});
	console.log();

	// Test 3: Dictionary parsing (first few entries)
	console.log('3️⃣  Testing dictionary parsing:');
	profile('Parse first 1000 entries', () => {
		let o = 0;
		const nEntries = Math.min(1000, dv!.getUint32(o, true));
		o += 4;
		
		for (let idx = 0; idx < nEntries; idx++) {
			const wordBytes = dv!.getUint16(o, true);
			o += 2;
			const score = dv!.getUint16(o, true);
			o += 2;
			const wlen = dv!.getUint8(o);
			o += 1;
			// Skip histogram
			o += 26;
			// Skip word bytes
			o += wordBytes;
		}
	});
	console.log();

	// Test 4: Full dictionary parsing
	console.log('4️⃣  Testing full dictionary parsing:');
	let totalEntries = 0;
	profile('Parse all entries', () => {
		let o = 0;
		const nEntries = dv!.getUint32(o, true);
		totalEntries = nEntries;
		o += 4;
		
		for (let idx = 0; idx < nEntries; idx++) {
			const wordBytes = dv!.getUint16(o, true);
			o += 2;
			const score = dv!.getUint16(o, true);
			o += 2;
			const wlen = dv!.getUint8(o);
			o += 1;
			// Skip histogram
			o += 26;
			// Skip word bytes
			o += wordBytes;
		}
	});
	console.log(`   Total entries: ${totalEntries}\n`);

	// Test 5: Board summarization
	console.log('5️⃣  Testing board operations:');
	const board = createTestBoard();
	
	profile('Board creation', () => {
		for (let i = 0; i < 1000; i++) {
			createTestBoard();
		}
	});

	// Test board summarization (from bestWords)
	profile('Board summarization', () => {
		for (let i = 0; i < 1000; i++) {
			const freq = new Uint8Array(26);
			const tiles: any[][] = Array.from({ length: 26 }, (_) => []);
			for (const row of board.tiles) {
				for (const t of row) {
					const idx = t.letter.charCodeAt(0) - 65;
					freq[idx]++;
					tiles[idx].push(t);
				}
			}
		}
	});
	console.log();

	console.log('✅ Bottleneck analysis complete');
}

main().catch(console.error);
