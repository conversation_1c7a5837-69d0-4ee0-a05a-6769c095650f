/**
 * Comprehensive test script for the optimized LettersBot solver on the live website
 *
 * This script tests the optimized solver implementation against real game scenarios
 * from the live LettersBot website, validating performance, accuracy, and functionality.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard, injectHelperFunctions, createTestBoard } from '../lib/solver/scraping';
import { bestWordsFast } from '../lib/bestWordsFast';
import {
	playWord,
	getCurrentWord,
	isGameReady,
	waitForGameReady
} from '../lib/solver/gameInteraction';
import { Board } from '../lib/models/Board';
import { Word } from '../lib/models/Word';
import { calculateScoringBreakdown } from '../lib/utils/scoring';
import { isValidWordPath } from '../lib/utils/wordValidation';

interface TestResult {
	boardState: string;
	solverTime: number;
	wordsFound: number;
	topWord: Word | null;
	validationResults: ValidationResult[];
	playbackResults: PlaybackResult[];
	errors: string[];
}

interface ValidationResult {
	word: Word;
	isValid: boolean;
	usesAvailableTiles: boolean;
	followsPlacementRules: boolean;
	scoreCalculationCorrect: boolean;
	error?: string;
}

interface PlaybackResult {
	word: Word;
	success: boolean;
	actualScore?: number;
	expectedScore: number;
	error?: string;
}

interface TestConfig {
	headless: boolean;
	slowMo: number;
	timeout: number;
	maxWordsToTest: number;
	maxWordsToPlay: number;
	takeScreenshots: boolean;
	testMultipleBoards: boolean;
	maxBoardTests: number;
}

const DEFAULT_CONFIG: TestConfig = {
	headless: false,
	slowMo: 500,
	timeout: 60000, // Increased timeout
	maxWordsToTest: 10,
	maxWordsToPlay: 2, // Reduced for faster testing
	takeScreenshots: true,
	testMultipleBoards: false, // Start with single board test
	maxBoardTests: 1
};

async function main() {
	console.log('🤖 Testing Optimized LettersBot Solver on Live Website');
	console.log('=====================================================\n');

	const config = DEFAULT_CONFIG;
	let browser: Browser | null = null;
	let page: Page | null = null;
	const testResults: TestResult[] = [];

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({
			headless: config.headless,
			slowMo: config.slowMo
		});

		page = await browser.newPage();
		page.setDefaultTimeout(config.timeout);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await navigateToGame(page, config);

		// Test solver on multiple board states
		if (config.testMultipleBoards) {
			for (let i = 0; i < config.maxBoardTests; i++) {
				console.log(`\n3.${i + 1}. Testing board state ${i + 1}/${config.maxBoardTests}...`);

				try {
					const result = await testSolverOnCurrentBoard(page, config, i + 1);
					testResults.push(result);

					// Start a new game for the next test (if not the last one)
					if (i < config.maxBoardTests - 1) {
						await startNewGame(page);
					}
				} catch (error) {
					console.error(`   ❌ Error testing board ${i + 1}:`, error);
					testResults.push({
						boardState: `Board ${i + 1} - Error`,
						solverTime: 0,
						wordsFound: 0,
						topWord: null,
						validationResults: [],
						playbackResults: [],
						errors: [String(error)]
					});
				}
			}
		} else {
			// Test single board
			console.log('\n3. Testing current board state...');
			const result = await testSolverOnCurrentBoard(page, config, 1);
			testResults.push(result);
		}

		// Generate comprehensive report
		console.log('\n4. Generating test report...');
		await generateTestReport(testResults);

		console.log('\n✅ All tests completed successfully!');
	} catch (error) {
		console.error('❌ Error during testing:', error);
		console.error('Stack trace:', (error as Error).stack);

		if (page && config.takeScreenshots) {
			try {
				await page.screenshot({
					path: 'screenshots/live-solver-test-error.png',
					fullPage: true
				});
				console.log('   📸 Error screenshot saved');
			} catch (screenshotError) {
				console.error('Failed to take error screenshot:', screenshotError);
			}
		}
	} finally {
		if (browser) {
			console.log('\n5. Closing browser...');
			await browser.close();
		}
	}
}

/**
 * Navigate to the Letters game and ensure it's ready
 */
async function navigateToGame(page: Page, config: TestConfig): Promise<void> {
	await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });

	// Wait for page to load
	await page.waitForTimeout(3000);

	if (config.takeScreenshots) {
		await page.screenshot({
			path: 'screenshots/live-solver-test-landing.png',
			fullPage: true
		});
	}

	// Look for and click "Play on Web" button
	try {
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(3000);
		}
	} catch (error) {
		console.log('   ⚠️  "Play on Web" button not found, proceeding...');
	}

	// Wait for game to be ready (with fallback)
	try {
		await waitForGameReady(page);
		console.log('   ✅ Game is ready for interaction');
	} catch (error) {
		console.log('   ⚠️  Game readiness check failed, proceeding anyway...');
		console.log(`   Error: ${error}`);
	}

	if (config.takeScreenshots) {
		await page.screenshot({
			path: 'screenshots/live-solver-test-game-ready.png',
			fullPage: true
		});
	}

	// Inject helper functions
	try {
		await injectHelperFunctions(page);
		console.log('   ✅ Helper functions injected');
	} catch (error) {
		console.log('   ⚠️  Helper function injection failed, proceeding anyway...');
		console.log(`   Error: ${error}`);
	}
}

/**
 * Test the solver on the current board state
 */
async function testSolverOnCurrentBoard(
	page: Page,
	config: TestConfig,
	boardNumber: number
): Promise<TestResult> {
	const result: TestResult = {
		boardState: `Board ${boardNumber}`,
		solverTime: 0,
		wordsFound: 0,
		topWord: null,
		validationResults: [],
		playbackResults: [],
		errors: []
	};

	try {
		// Scrape current board state
		console.log('   📋 Scraping board state...');
		let board: Board;

		try {
			board = await scrapeBoard(page);
			console.log('   ✅ Successfully scraped live board');
		} catch (scrapingError) {
			console.log('   ⚠️  Board scraping failed, using test board...');
			console.log(`   Scraping error: ${scrapingError}`);
			board = createTestBoard();
			result.errors.push(`Board scraping failed: ${scrapingError}`);
		}

		result.boardState = board.toString();

		if (config.takeScreenshots) {
			await page.screenshot({
				path: `screenshots/live-solver-test-board-${boardNumber}.png`,
				fullPage: true
			});
		}

		// Run optimized solver
		console.log('   🧠 Running optimized solver...');
		const startTime = Date.now();
		const words = bestWordsFast(board, config.maxWordsToTest);
		const endTime = Date.now();

		result.solverTime = endTime - startTime;
		result.wordsFound = words.length;
		result.topWord = words.length > 0 ? words[0] : null;

		console.log(`   ⏱️  Solver completed in ${result.solverTime}ms`);
		console.log(`   📊 Found ${result.wordsFound} words`);

		if (words.length > 0) {
			console.log(`   🏆 Top word: ${words[0].letters} (${words[0].score} points)`);

			// Validate solver results
			console.log('   ✅ Validating solver results...');
			result.validationResults = await validateSolverResults(words, board);

			// Test word playing
			console.log('   🎮 Testing word playing...');
			result.playbackResults = await testWordPlaying(page, words.slice(0, config.maxWordsToPlay));
		} else {
			result.errors.push('No words found by solver');
		}
	} catch (error) {
		result.errors.push(String(error));
		console.error('   ❌ Error during board testing:', error);
	}

	return result;
}

/**
 * Validate solver results for correctness
 */
async function validateSolverResults(words: Word[], board: Board): Promise<ValidationResult[]> {
	const results: ValidationResult[] = [];

	for (const word of words) {
		const validation: ValidationResult = {
			word,
			isValid: true,
			usesAvailableTiles: true,
			followsPlacementRules: true,
			scoreCalculationCorrect: true
		};

		try {
			// Check if word uses only available tiles
			const availableTiles = board.getAllTiles();
			const usedLetters = word.letters.split('');
			const availableLetters = availableTiles.map((t) => t.letter);

			for (const letter of usedLetters) {
				const letterIndex = availableLetters.indexOf(letter);
				if (letterIndex === -1) {
					validation.usesAvailableTiles = false;
					validation.isValid = false;
					validation.error = `Letter ${letter} not available on board`;
					break;
				}
				// Remove used letter to prevent double-counting
				availableLetters.splice(letterIndex, 1);
			}

			// Check placement rules (positions should be valid and connected)
			if (validation.usesAvailableTiles) {
				try {
					const isValidPlacement = isValidWordPath(word.positions);
					if (!isValidPlacement) {
						validation.followsPlacementRules = false;
						validation.isValid = false;
						validation.error = 'Invalid word placement';
					}
				} catch (error) {
					validation.followsPlacementRules = false;
					validation.isValid = false;
					validation.error = `Placement validation error: ${error}`;
				}
			}

			// Check score calculation
			if (validation.isValid) {
				try {
					const scoreBreakdown = calculateScoringBreakdown(word, board);
					if (Math.abs(scoreBreakdown.totalScore - word.score) > 0.01) {
						validation.scoreCalculationCorrect = false;
						validation.isValid = false;
						validation.error = `Score mismatch: expected ${scoreBreakdown.totalScore}, got ${word.score}`;
					}
				} catch (error) {
					validation.scoreCalculationCorrect = false;
					validation.isValid = false;
					validation.error = `Score calculation error: ${error}`;
				}
			}
		} catch (error) {
			validation.isValid = false;
			validation.error = `Validation error: ${error}`;
		}

		results.push(validation);
	}

	return results;
}

/**
 * Test playing words on the live website
 */
async function testWordPlaying(page: Page, words: Word[]): Promise<PlaybackResult[]> {
	const results: PlaybackResult[] = [];

	for (const word of words) {
		const playbackResult: PlaybackResult = {
			word,
			success: false,
			expectedScore: word.score
		};

		try {
			console.log(`     🎯 Attempting to play: ${word.letters}`);

			// Get current score before playing
			const scoreBefore = await getCurrentScore(page);

			// Play the word
			await playWord(page, word.positions);

			// Wait for score update
			await page.waitForTimeout(2000);

			// Get score after playing
			const scoreAfter = await getCurrentScore(page);

			if (scoreAfter > scoreBefore) {
				playbackResult.success = true;
				playbackResult.actualScore = scoreAfter - scoreBefore;
				console.log(`     ✅ Successfully played! Score: ${playbackResult.actualScore}`);
			} else {
				playbackResult.error = 'Score did not increase after playing word';
				console.log(`     ❌ Word was not accepted`);
			}
		} catch (error) {
			playbackResult.error = String(error);
			console.log(`     ❌ Error playing word: ${error}`);
		}

		results.push(playbackResult);

		// Add delay between word attempts
		await page.waitForTimeout(1000);
	}

	return results;
}

/**
 * Get current score from the game interface
 */
async function getCurrentScore(page: Page): Promise<number> {
	const scoreSelectors = [
		'.score',
		'#score',
		'[data-testid="score"]',
		'.game-score',
		'.total-score'
	];

	for (const selector of scoreSelectors) {
		try {
			const element = await page.$(selector);
			if (element) {
				const scoreText = await element.textContent();
				const score = parseInt(scoreText?.replace(/\D/g, '') || '0');
				if (!isNaN(score)) {
					return score;
				}
			}
		} catch {
			// Try next selector
		}
	}

	return 0; // Default if score not found
}

/**
 * Start a new game
 */
async function startNewGame(page: Page): Promise<void> {
	const newGameSelectors = [
		'button:has-text("New Game")',
		'button:has-text("Start New")',
		'button:has-text("Reset")',
		'.new-game-button',
		'#new-game'
	];

	for (const selector of newGameSelectors) {
		try {
			const element = await page.$(selector);
			if (element) {
				await element.click();
				await page.waitForTimeout(3000);
				await waitForGameReady(page);
				return;
			}
		} catch {
			// Try next selector
		}
	}

	// If no new game button found, refresh the page
	console.log('   🔄 No new game button found, refreshing page...');
	await page.reload({ waitUntil: 'networkidle' });
	await page.waitForTimeout(3000);
	await waitForGameReady(page);
}

/**
 * Generate comprehensive test report
 */
async function generateTestReport(testResults: TestResult[]): Promise<void> {
	console.log('\n📊 COMPREHENSIVE TEST REPORT');
	console.log('============================\n');

	// Overall statistics
	const totalTests = testResults.length;
	const successfulTests = testResults.filter((r) => r.errors.length === 0).length;
	const totalWordsFound = testResults.reduce((sum, r) => sum + r.wordsFound, 0);
	const totalSolverTime = testResults.reduce((sum, r) => sum + r.solverTime, 0);
	const avgSolverTime = totalTests > 0 ? totalSolverTime / totalTests : 0;

	console.log(`📈 Overall Statistics:`);
	console.log(`   Tests completed: ${successfulTests}/${totalTests}`);
	console.log(`   Total words found: ${totalWordsFound}`);
	console.log(`   Average solver time: ${avgSolverTime.toFixed(2)}ms`);
	console.log(`   Total solver time: ${totalSolverTime}ms\n`);

	// Performance analysis
	console.log(`⚡ Performance Analysis:`);
	const solverTimes = testResults.map((r) => r.solverTime).filter((t) => t > 0);
	if (solverTimes.length > 0) {
		const minTime = Math.min(...solverTimes);
		const maxTime = Math.max(...solverTimes);
		console.log(`   Fastest solve: ${minTime}ms`);
		console.log(`   Slowest solve: ${maxTime}ms`);
		console.log(
			`   Performance rating: ${avgSolverTime < 1000 ? '🟢 Excellent' : avgSolverTime < 3000 ? '🟡 Good' : '🔴 Needs improvement'}`
		);
	}
	console.log();

	// Validation results
	const allValidations = testResults.flatMap((r) => r.validationResults);
	const validWords = allValidations.filter((v) => v.isValid).length;
	const tileValidations = allValidations.filter((v) => v.usesAvailableTiles).length;
	const placementValidations = allValidations.filter((v) => v.followsPlacementRules).length;
	const scoreValidations = allValidations.filter((v) => v.scoreCalculationCorrect).length;

	console.log(`✅ Validation Results:`);
	console.log(
		`   Valid words: ${validWords}/${allValidations.length} (${((validWords / allValidations.length) * 100).toFixed(1)}%)`
	);
	console.log(
		`   Correct tile usage: ${tileValidations}/${allValidations.length} (${((tileValidations / allValidations.length) * 100).toFixed(1)}%)`
	);
	console.log(
		`   Valid placements: ${placementValidations}/${allValidations.length} (${((placementValidations / allValidations.length) * 100).toFixed(1)}%)`
	);
	console.log(
		`   Correct scores: ${scoreValidations}/${allValidations.length} (${((scoreValidations / allValidations.length) * 100).toFixed(1)}%)`
	);
	console.log();

	// Playback results
	const allPlaybacks = testResults.flatMap((r) => r.playbackResults);
	const successfulPlays = allPlaybacks.filter((p) => p.success).length;

	console.log(`🎮 Playback Results:`);
	console.log(
		`   Successful plays: ${successfulPlays}/${allPlaybacks.length} (${((successfulPlays / allPlaybacks.length) * 100).toFixed(1)}%)`
	);

	if (successfulPlays > 0) {
		const scoreAccuracies = allPlaybacks
			.filter((p) => p.success && p.actualScore !== undefined)
			.map((p) => Math.abs((p.actualScore! - p.expectedScore) / p.expectedScore));

		if (scoreAccuracies.length > 0) {
			const avgAccuracy =
				scoreAccuracies.reduce((sum, acc) => sum + acc, 0) / scoreAccuracies.length;
			console.log(`   Score accuracy: ${((1 - avgAccuracy) * 100).toFixed(1)}%`);
		}
	}
	console.log();

	// Detailed results for each test
	testResults.forEach((result, index) => {
		console.log(`📋 Test ${index + 1} Details:`);
		console.log(`   Board: ${result.boardState.split('\n')[0]}...`);
		console.log(`   Solver time: ${result.solverTime}ms`);
		console.log(`   Words found: ${result.wordsFound}`);

		if (result.topWord) {
			console.log(`   Top word: ${result.topWord.letters} (${result.topWord.score} points)`);
		}

		if (result.errors.length > 0) {
			console.log(`   ❌ Errors: ${result.errors.join(', ')}`);
		}

		const validationSuccess = result.validationResults.filter((v) => v.isValid).length;
		const playbackSuccess = result.playbackResults.filter((p) => p.success).length;

		console.log(`   Validation: ${validationSuccess}/${result.validationResults.length} valid`);
		console.log(`   Playback: ${playbackSuccess}/${result.playbackResults.length} successful`);
		console.log();
	});

	// Recommendations
	console.log(`💡 Recommendations:`);

	if (avgSolverTime > 3000) {
		console.log(
			`   🔧 Consider optimizing solver performance (current avg: ${avgSolverTime.toFixed(2)}ms)`
		);
	}

	if (validWords / allValidations.length < 0.9) {
		console.log(
			`   🔧 Improve word validation logic (${((validWords / allValidations.length) * 100).toFixed(1)}% valid)`
		);
	}

	if (successfulPlays / allPlaybacks.length < 0.8) {
		console.log(
			`   🔧 Improve word playing automation (${((successfulPlays / allPlaybacks.length) * 100).toFixed(1)}% success rate)`
		);
	}

	if (totalWordsFound / totalTests < 5) {
		console.log(
			`   🔧 Solver may not be finding enough words (avg: ${(totalWordsFound / totalTests).toFixed(1)} per board)`
		);
	}

	console.log(
		`   ✅ Overall solver quality: ${validWords / allValidations.length > 0.9 && avgSolverTime < 3000 ? 'Excellent' : validWords / allValidations.length > 0.8 && avgSolverTime < 5000 ? 'Good' : 'Needs improvement'}`
	);
}

// Run the test
main().catch(console.error);
