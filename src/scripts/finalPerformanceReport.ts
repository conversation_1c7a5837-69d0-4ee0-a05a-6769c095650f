/**
 * Final performance report comparing all optimization approaches
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { bestWordsFast } from '../lib/bestWordsFast';
import { bestWordsOptimized } from '../lib/bestWordsOptimized';

// Profile a function with detailed stats
function profileDetailed<T>(name: string, fn: () => T, iterations: number = 1): {
	result: T;
	avgTime: number;
	minTime: number;
	maxTime: number;
	stdDev: number;
} {
	const times: number[] = [];
	let result: T;

	for (let i = 0; i < iterations; i++) {
		const start = performance.now();
		result = fn();
		const end = performance.now();
		times.push(end - start);
	}

	const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
	const minTime = Math.min(...times);
	const maxTime = Math.max(...times);
	const variance = times.reduce((acc, time) => acc + Math.pow(time - avgTime, 2), 0) / times.length;
	const stdDev = Math.sqrt(variance);

	console.log(`⏱️  ${name}:`);
	console.log(`     Average: ${avgTime.toFixed(2)}ms`);
	console.log(`     Min: ${minTime.toFixed(2)}ms`);
	console.log(`     Max: ${maxTime.toFixed(2)}ms`);
	console.log(`     Std Dev: ${stdDev.toFixed(2)}ms`);

	return { result: result!, avgTime, minTime, maxTime, stdDev };
}

function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			// Add realistic multipliers
			if ((row + col) % 3 === 0) tile.letterMult = 2;
			if ((row + col) % 7 === 0) tile.wordMult = 2;
			board.setTile(row, col, tile);
		}
	}
	return board;
}

function createRandomBoard(seed: number): Board {
	// Deterministic random board for consistent testing
	const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
	const board = new Board();
	
	let rng = seed;
	const random = () => {
		rng = (rng * 9301 + 49297) % 233280;
		return rng / 233280;
	};
	
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const randomLetter = letters[Math.floor(random() * letters.length)];
			const tile = new Tile(randomLetter, row, col);
			
			if (random() < 0.2) tile.letterMult = 2;
			if (random() < 0.1) tile.wordMult = 2;
			if (random() < 0.05) tile.letterMult = 3;
			if (random() < 0.02) tile.wordMult = 3;
			
			board.setTile(row, col, tile);
		}
	}
	return board;
}

async function main() {
	console.log('📊 Final Performance Report');
	console.log('===========================\n');

	const testBoards = [
		{ name: 'Standard Test Board', board: createTestBoard() },
		{ name: 'Random Board A', board: createRandomBoard(12345) },
		{ name: 'Random Board B', board: createRandomBoard(67890) },
		{ name: 'Random Board C', board: createRandomBoard(54321) }
	];

	const results: any[] = [];

	for (const { name, board } of testBoards) {
		console.log(`🎯 Testing: ${name}`);
		console.log('Board layout:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row].map(t => 
				`${t.letter}${t.letterMult > 1 ? `(${t.letterMult}L)` : ''}${t.wordMult > 1 ? `(${t.wordMult}W)` : ''}`
			).join(' ');
			console.log(`   ${rowStr}`);
		}
		console.log();

		// Test different K values
		for (const K of [10, 25, 50]) {
			console.log(`  📈 K = ${K}:`);

			// Test Ultra-Fast version
			const ultraFastResult = profileDetailed(
				`Ultra-Fast (K=${K})`,
				() => bestWordsFast(board, K),
				20
			);

			// Test Optimized version
			const optimizedResult = profileDetailed(
				`Optimized (K=${K})`,
				() => bestWordsOptimized(board, K),
				20
			);

			// Compare results
			console.log(`     Words found: Ultra-Fast=${ultraFastResult.result.length}, Optimized=${optimizedResult.result.length}`);
			
			const speedup = optimizedResult.avgTime / ultraFastResult.avgTime;
			console.log(`     🚀 Ultra-Fast is ${speedup.toFixed(1)}x faster than Optimized`);

			// Quality comparison
			const ultraFastScores = ultraFastResult.result.slice(0, 5).map(w => w.score);
			const optimizedScores = optimizedResult.result.slice(0, 5).map(w => w.score);
			console.log(`     Top 5 scores - Ultra-Fast: [${ultraFastScores.join(', ')}]`);
			console.log(`     Top 5 scores - Optimized: [${optimizedScores.join(', ')}]`);

			results.push({
				board: name,
				K,
				ultraFast: ultraFastResult,
				optimized: optimizedResult,
				speedup
			});

			console.log();
		}
		console.log('─'.repeat(60));
	}

	// Overall statistics
	console.log('\n📈 Overall Performance Summary:');
	console.log('==============================');

	const avgUltraFast = results.reduce((sum, r) => sum + r.ultraFast.avgTime, 0) / results.length;
	const avgOptimized = results.reduce((sum, r) => sum + r.optimized.avgTime, 0) / results.length;
	const avgSpeedup = results.reduce((sum, r) => sum + r.speedup, 0) / results.length;

	console.log(`Average Ultra-Fast time: ${avgUltraFast.toFixed(2)}ms`);
	console.log(`Average Optimized time: ${avgOptimized.toFixed(2)}ms`);
	console.log(`Average speedup: ${avgSpeedup.toFixed(1)}x`);

	// Memory usage test
	console.log('\n💾 Memory Usage Analysis:');
	const memBefore = process.memoryUsage();
	console.log(`Memory before: ${(memBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`);

	// Run many iterations to test for memory leaks
	const board = createTestBoard();
	for (let i = 0; i < 200; i++) {
		bestWordsFast(board, 50);
		if (i % 50 === 0) {
			// Force garbage collection if available
			if (global.gc) {
				global.gc();
			}
		}
	}

	const memAfter = process.memoryUsage();
	console.log(`Memory after 200 runs: ${(memAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`);
	console.log(`Memory increase: ${((memAfter.heapUsed - memBefore.heapUsed) / 1024 / 1024).toFixed(2)} MB`);

	// Performance vs Original estimate
	console.log('\n🎯 Performance vs Original Implementation:');
	console.log('=========================================');
	console.log('Original bestWords function: >10,000ms (timed out)');
	console.log(`Ultra-Fast implementation: ${avgUltraFast.toFixed(2)}ms`);
	console.log(`Improvement: >${(10000 / avgUltraFast).toFixed(0)}x faster`);

	// Recommendations
	console.log('\n💡 Recommendations:');
	console.log('==================');
	console.log('1. ✅ Use Ultra-Fast implementation for production');
	console.log('2. 🦀 Consider Rust WebAssembly for 10-25x additional speedup');
	console.log('3. 🔄 Implement caching for repeated board states');
	console.log('4. ⚡ Use Web Workers for non-blocking execution');
	console.log('5. 📊 Monitor performance in production with telemetry');

	console.log('\n✅ Performance analysis complete');
}

main().catch(console.error);
