/**
 * Test the full dictionary processing to identify the exact bottleneck
 */

import { readFileSync } from 'node:fs';
import { join } from 'path';
import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { Word } from '../lib/models/Word';
import { solveAssignment } from '../lib/solver/hungarian';

// Profile a function
function profile<T>(name: string, fn: () => T): T {
	const start = performance.now();
	const result = fn();
	const end = performance.now();
	console.log(`⏱️  ${name}: ${(end - start).toFixed(2)}ms`);
	return result;
}

function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			// Add some multipliers
			if ((row + col) % 3 === 0) tile.letterMult = 2;
			if ((row + col) % 7 === 0) tile.wordMult = 2;
			board.setTile(row, col, tile);
		}
	}
	return board;
}

// Simplified Hungarian algorithm for placement scoring
function bestPlacementScore(word: string, tilesBag: Tile[][], letterScoreSum: number): number {
	const LETTER_SCORES: number[] = Array(26).fill(0);
	Object.assign(LETTER_SCORES, {
		0: 1, 1: 4, 2: 4, 3: 2, 4: 1, 5: 4, 6: 2, 7: 4, 8: 1, 9: 10,
		10: 5, 11: 1, 12: 4, 13: 2, 14: 1, 15: 3, 16: 10, 17: 1, 18: 1, 19: 1,
		20: 2, 21: 5, 22: 4, 23: 8, 24: 3, 25: 10
	});

	const n = word.length;
	const M = Array.from<number[], number>({ length: n }, () => new Array(n).fill(0));

	// Build cost matrix
	const letterCounts: Record<string, number> = {};
	for (let i = 0; i < n; i++) {
		const ch = word[i];
		letterCounts[ch] = (letterCounts[ch] ?? 0) + 1;
		const tileList = tilesBag[ch.charCodeAt(0) - 65];
		const startIdx = letterCounts[ch] - 1;
		for (let j = 0; j < n; j++) M[i][j] = 999999;
		
		let col = 0;
		for (let tIdx = startIdx; tIdx < tileList.length; tIdx++) {
			const tile = tileList[tIdx];
			const letterValue = LETTER_SCORES[ch.charCodeAt(0) - 65] * tile.letterMult;
			const wordMult = tile.wordMult;
			M[i][col++] = -letterValue - (wordMult > 1 ? 1e4 * wordMult : 0);
		}
	}

	const result = solveAssignment(M);
	if (!result.success || result.assignment.length !== n) {
		return letterScoreSum;
	}

	let letterSum = 0, wordMultTotal = 1;
	for (let r = 0; r < n; r++) {
		const letterIdx = word[r].charCodeAt(0) - 65;
		const tileList = tilesBag[letterIdx];
		const assignedCol = result.assignment[r];

		if (assignedCol < 0 || assignedCol >= tileList.length) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		const tile = tileList[assignedCol];
		if (!tile) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		letterSum += LETTER_SCORES[letterIdx] * tile.letterMult;
		wordMultTotal *= tile.wordMult;
	}
	return letterSum * wordMultTotal;
}

async function main() {
	console.log('🔍 Testing Full Dictionary Processing');
	console.log('====================================\n');

	const board = createTestBoard();
	const K = 50;

	// Load dictionary
	const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
	const dictBytes = readFileSync(DICT_BIN);
	const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
	const textDec = new TextDecoder();

	// Parse dictionary entries
	const ENTRIES = profile('Parse all entries', () => {
		let o = 0;
		const nEntries = dv.getUint32(o, true);
		o += 4;
		const out: any[] = new Array(nEntries);

		for (let idx = 0; idx < nEntries; idx++) {
			const wordBytes = dv.getUint16(o, true);
			o += 2;
			const score = dv.getUint16(o, true);
			o += 2;
			const wlen = dv.getUint8(o);
			o += 1;
			const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
			o += 26;

			out[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
			o += wordBytes;
		}
		return out;
	});

	// Board summarization
	const bag = profile('Summarize board', () => {
		const freq = new Uint8Array(26);
		const tiles: Tile[][] = Array.from({ length: 26 }, (_) => []);
		for (const row of board.tiles) {
			for (const t of row) {
				const idx = t.letter.charCodeAt(0) - 65;
				freq[idx]++;
				tiles[idx].push(t);
			}
		}
		return { freq, tiles };
	});

	console.log(`\nProcessing ${ENTRIES.length} dictionary entries...\n`);

	// Test with different approaches
	console.log('1️⃣  Without Hungarian algorithm (base scores only):');
	const result1 = profile('Full dictionary - no Hungarian', () => {
		const candid: Word[] = [];
		let worstScore = 0;
		let hungarianCalls = 0;

		for (const meta of ENTRIES) {
			if (meta.wordLen > 25) continue;

			let ok = true;
			for (let i = 0; i < 26; i++) {
				if (meta.hist[i] > bag.freq[i]) {
					ok = false;
					break;
				}
			}
			if (!ok) continue;

			const word = textDec.decode(
				new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
			);

			const score = meta.letterScoreSum; // Use base score only
			if (score < worstScore) continue;

			const dummyPositions: Array<[number, number]> = [];
			for (let i = 0; i < word.length; i++) {
				dummyPositions.push([0, i]);
			}
			const wordObj = new Word(word, dummyPositions, score);
			candid.push(wordObj);
			candid.sort((a, b) => b.score - a.score);
			if (candid.length > K) candid.pop();
			worstScore = candid.length === K ? candid[candid.length - 1].score : 0;
		}

		return { words: candid, hungarianCalls };
	});
	console.log(`   Found ${result1.words.length} words\n`);

	console.log('2️⃣  With Hungarian algorithm (full scoring):');
	const result2 = profile('Full dictionary - with Hungarian', () => {
		const candid: Word[] = [];
		let worstScore = 0;
		let hungarianCalls = 0;

		for (const meta of ENTRIES) {
			if (meta.wordLen > 25) continue;

			let ok = true;
			for (let i = 0; i < 26; i++) {
				if (meta.hist[i] > bag.freq[i]) {
					ok = false;
					break;
				}
			}
			if (!ok) continue;

			const word = textDec.decode(
				new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
			);

			hungarianCalls++;
			const score = bestPlacementScore(word, bag.tiles, meta.letterScoreSum);
			if (score < worstScore) continue;

			const dummyPositions: Array<[number, number]> = [];
			for (let i = 0; i < word.length; i++) {
				dummyPositions.push([0, i]);
			}
			const wordObj = new Word(word, dummyPositions, score);
			candid.push(wordObj);
			candid.sort((a, b) => b.score - a.score);
			if (candid.length > K) candid.pop();
			worstScore = candid.length === K ? candid[candid.length - 1].score : 0;
		}

		return { words: candid, hungarianCalls };
	});
	console.log(`   Found ${result2.words.length} words`);
	console.log(`   Hungarian calls: ${result2.hungarianCalls}\n`);

	console.log('✅ Full dictionary test complete');
}

main().catch(console.error);
