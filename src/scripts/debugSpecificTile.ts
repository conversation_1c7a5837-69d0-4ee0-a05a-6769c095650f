/**
 * Debug script to examine a specific tile position [4][4] for multiplier detection
 * 
 * This script specifically investigates why the 3x letter multiplier at position [4][4]
 * is not being detected.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { injectHelperFunctions } from '../lib/solver/scraping';

async function main() {
	console.log('🔍 Debugging Specific Tile [4][4] Multiplier Detection');
	console.log('=====================================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({ 
			headless: false,
			slowMo: 1000
		});
		page = await browser.newPage();

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/');
		
		// Wait for page to load
		await page.waitForTimeout(3000);

		// Look for "Play on Web" button and click it
		console.log('3. Looking for "Play on Web" button...');
		const playButton = await page.$('text=Play on Web');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(3000);
		}

		// Inject helper functions
		console.log('4. Injecting helper functions...');
		await injectHelperFunctions(page);

		// Debug specific tile at position [4][4] (bottom-right corner)
		console.log('5. Debugging tile at position [4][4]...');
		await debugSpecificTile(page, 4, 4);

		// Also check a few other tiles for comparison
		console.log('\n6. Checking other tiles for comparison...');
		await debugSpecificTile(page, 0, 0); // Top-left
		await debugSpecificTile(page, 2, 2); // Center
		await debugSpecificTile(page, 1, 1); // Known multiplier position

		// Keep browser open for manual inspection
		console.log('\n🔍 Browser will stay open for manual inspection...');
		console.log('   Check the console for detailed multiplier detection logs');
		console.log('   Press Ctrl+C to exit when done');
		
		// Wait indefinitely
		await new Promise(() => {});

	} catch (error) {
		console.error('❌ Debug failed:', error);
	}
}

/**
 * Debug a specific tile position
 */
async function debugSpecificTile(page: Page, row: number, col: number): Promise<void> {
	console.log(`\n🔍 Debugging tile at position [${row}][${col}]:`);

	// Get the tile element using grid position
	const tileIndex = row * 5 + col;
	const tileSelector = `.grid.grid-cols-5 > div:nth-child(${tileIndex + 1})`;
	
	console.log(`   🎯 Using selector: ${tileSelector}`);

	const tileElement = await page.$(tileSelector);
	if (!tileElement) {
		console.log(`   ❌ Could not find tile element`);
		return;
	}

	// Get detailed information about this tile
	const tileInfo = await tileElement.evaluate((element) => {
		// Get basic element info
		const rect = element.getBoundingClientRect();
		const computedStyle = window.getComputedStyle(element);
		
		// Get all child elements and their info
		const children = Array.from(element.children).map(child => ({
			tagName: child.tagName,
			className: child.className,
			textContent: child.textContent,
			innerHTML: child.innerHTML.substring(0, 100),
			style: {
				backgroundColor: window.getComputedStyle(child).backgroundColor,
				borderColor: window.getComputedStyle(child).borderColor,
				color: window.getComputedStyle(child).color
			}
		}));

		// Look for SVG elements specifically
		const svgElements = Array.from(element.querySelectorAll('svg')).map(svg => ({
			outerHTML: svg.outerHTML,
			className: svg.className.baseVal || svg.className,
			textContent: svg.textContent
		}));

		// Look for any elements with specific classes or attributes
		const multiplierElements = Array.from(element.querySelectorAll('*')).filter(el => {
			const classes = el.className.toString().toLowerCase();
			const text = el.textContent?.toLowerCase() || '';
			return classes.includes('mult') || classes.includes('bonus') || 
				   text.includes('2x') || text.includes('3x') ||
				   classes.includes('letter') || classes.includes('word');
		}).map(el => ({
			tagName: el.tagName,
			className: el.className,
			textContent: el.textContent,
			innerHTML: el.innerHTML.substring(0, 100)
		}));

		return {
			position: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
			classes: element.className,
			innerHTML: element.innerHTML,
			style: {
				backgroundColor: computedStyle.backgroundColor,
				borderColor: computedStyle.borderColor,
				boxShadow: computedStyle.boxShadow,
				color: computedStyle.color
			},
			children,
			svgElements,
			multiplierElements,
			textContent: element.textContent
		};
	});

	console.log(`   📍 Position: ${JSON.stringify(tileInfo.position)}`);
	console.log(`   🏷️  Classes: ${tileInfo.classes}`);
	console.log(`   📝 Text: "${tileInfo.textContent}"`);
	console.log(`   🎨 Background: ${tileInfo.style.backgroundColor}`);
	console.log(`   🖼️  Border: ${tileInfo.style.borderColor}`);
	console.log(`   ✨ Box Shadow: ${tileInfo.style.boxShadow}`);
	
	if (tileInfo.children.length > 0) {
		console.log(`   👶 Children (${tileInfo.children.length}):`);
		tileInfo.children.forEach((child, index) => {
			console.log(`      ${index + 1}. ${child.tagName}.${child.className} - "${child.textContent}"`);
			console.log(`         Background: ${child.style.backgroundColor}`);
			console.log(`         Border: ${child.style.borderColor}`);
		});
	}

	if (tileInfo.svgElements.length > 0) {
		console.log(`   🖼️  SVG Elements (${tileInfo.svgElements.length}):`);
		tileInfo.svgElements.forEach((svg, index) => {
			console.log(`      ${index + 1}. Class: ${svg.className}`);
			console.log(`         Text: "${svg.textContent}"`);
			console.log(`         HTML: ${svg.outerHTML.substring(0, 200)}...`);
		});
	}

	if (tileInfo.multiplierElements.length > 0) {
		console.log(`   🎯 Potential Multiplier Elements (${tileInfo.multiplierElements.length}):`);
		tileInfo.multiplierElements.forEach((el, index) => {
			console.log(`      ${index + 1}. ${el.tagName}.${el.className} - "${el.textContent}"`);
		});
	}

	// Now test the multiplier detection function
	console.log(`   🧪 Testing multiplier detection:`);
	const multiplierResult = await tileElement.evaluate((element) => {
		try {
			const { letterMult, wordMult } = (window as any).extractMultipliersFromElement(element);
			return { letterMult, wordMult, success: true };
		} catch (error) {
			return { error: error.message, success: false };
		}
	});

	if (multiplierResult.success) {
		console.log(`   📊 Detected: L${multiplierResult.letterMult}×W${multiplierResult.wordMult}×`);
		if (row === 4 && col === 4 && multiplierResult.letterMult !== 3) {
			console.log(`   ⚠️  ISSUE: Expected L3× but got L${multiplierResult.letterMult}×`);
		}
	} else {
		console.log(`   ❌ Detection failed: ${multiplierResult.error}`);
	}
}

// Run the debug
if (import.meta.url === `file://${process.argv[1]}`) {
	main().catch(console.error);
}
