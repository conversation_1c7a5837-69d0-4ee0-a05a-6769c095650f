/**
 * Test script for validating the improved multiplier detection logic
 * 
 * This script tests the enhanced multiplier detection to ensure both
 * word and letter multipliers are correctly identified.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard, injectHelperFunctions } from '../lib/solver/scraping';

async function main() {
	console.log('🔍 Testing Enhanced Multiplier Detection');
	console.log('========================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({ 
			headless: false,
			slowMo: 1000 // Slow down for debugging
		});
		page = await browser.newPage();

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/');
		
		// Wait for page to load
		await page.waitForTimeout(3000);

		// Inject helper functions with enhanced multiplier detection
		console.log('3. Injecting enhanced helper functions...');
		await injectHelperFunctions(page);

		// Test multiplier detection on the current board
		console.log('4. Testing multiplier detection...');
		await testMultiplierDetection(page);

		// Try to scrape the board and see what multipliers are detected
		console.log('5. Scraping board with enhanced detection...');
		const board = await scrapeBoard(page);
		
		console.log('\n📊 Board with detected multipliers:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row].map(tile => {
				let tileStr = tile.letter;
				if (tile.letterMult > 1) tileStr += `(L${tile.letterMult})`;
				if (tile.wordMult > 1) tileStr += `(W${tile.wordMult})`;
				return tileStr.padEnd(8);
			}).join(' ');
			console.log(`   ${rowStr}`);
		}

		// Count detected multipliers
		let letterMultCount = 0;
		let wordMultCount = 0;
		for (let row = 0; row < 5; row++) {
			for (let col = 0; col < 5; col++) {
				const tile = board.tiles[row][col];
				if (tile.letterMult > 1) letterMultCount++;
				if (tile.wordMult > 1) wordMultCount++;
			}
		}

		console.log(`\n📈 Multiplier Detection Summary:`);
		console.log(`   Letter multipliers found: ${letterMultCount}`);
		console.log(`   Word multipliers found: ${wordMultCount}`);
		console.log(`   Total multipliers: ${letterMultCount + wordMultCount}`);

		if (letterMultCount > 0) {
			console.log('   ✅ Letter multiplier detection is working!');
		} else {
			console.log('   ⚠️  No letter multipliers detected - may need further investigation');
		}

		if (wordMultCount > 0) {
			console.log('   ✅ Word multiplier detection is working!');
		} else {
			console.log('   ⚠️  No word multipliers detected - may need further investigation');
		}

		// Keep browser open for manual inspection
		console.log('\n🔍 Browser will stay open for manual inspection...');
		console.log('   Check the console for detailed multiplier detection logs');
		console.log('   Press Ctrl+C to exit when done');
		
		// Wait indefinitely
		await new Promise(() => {});

	} catch (error) {
		console.error('❌ Test failed:', error);
	} finally {
		// Don't close browser automatically for debugging
		// if (browser) {
		// 	await browser.close();
		// }
	}
}

/**
 * Test multiplier detection by examining individual tile elements
 */
async function testMultiplierDetection(page: Page): Promise<void> {
	console.log('   🔍 Testing individual tile multiplier detection...');

	// Get all tile elements and test multiplier detection on each
	const tileElements = await page.$$('.grid > div, .tile, .letter-tile, .game-tile');
	
	if (tileElements.length === 0) {
		console.log('   ⚠️  No tile elements found for testing');
		return;
	}

	console.log(`   📋 Found ${tileElements.length} tile elements to test`);

	// Test first few tiles in detail
	const tilesToTest = Math.min(5, tileElements.length);
	for (let i = 0; i < tilesToTest; i++) {
		console.log(`\n   🔍 Testing tile ${i + 1}:`);
		
		const tileInfo = await tileElements[i].evaluate((element) => {
			// Call the injected function
			try {
				const { letterMult, wordMult } = (window as any).extractMultipliersFromElement(element);
				return {
					letterMult,
					wordMult,
					classes: element.className,
					innerHTML: element.innerHTML.substring(0, 200),
					backgroundColor: window.getComputedStyle(element).backgroundColor,
					borderColor: window.getComputedStyle(element).borderColor
				};
			} catch (error) {
				return {
					error: error.message,
					classes: element.className,
					innerHTML: element.innerHTML.substring(0, 200)
				};
			}
		});

		if ('error' in tileInfo) {
			console.log(`      ❌ Error: ${tileInfo.error}`);
		} else {
			console.log(`      📊 Result: L${tileInfo.letterMult}×W${tileInfo.wordMult}×`);
			console.log(`      🏷️  Classes: ${tileInfo.classes}`);
			console.log(`      🎨 Background: ${tileInfo.backgroundColor}`);
			console.log(`      🖼️  Border: ${tileInfo.borderColor}`);
			console.log(`      📝 HTML: ${tileInfo.innerHTML}...`);
		}
	}
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
	main().catch(console.error);
}
