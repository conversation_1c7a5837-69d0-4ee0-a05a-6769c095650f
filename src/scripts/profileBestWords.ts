/**
 * Profile the bestWords function step by step
 */

import { readFileSync } from 'node:fs';
import { join } from 'path';
import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { Word } from '../lib/models/Word';
import { solveAssignment } from '../lib/solver/hungarian';

// Profile a function
function profile<T>(name: string, fn: () => T): T {
	const start = performance.now();
	const result = fn();
	const end = performance.now();
	console.log(`⏱️  ${name}: ${(end - start).toFixed(2)}ms`);
	return result;
}

function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			board.setTile(row, col, tile);
		}
	}
	return board;
}

// Replicate the bestWords logic step by step
async function main() {
	console.log('🔍 Profiling bestWords Function');
	console.log('===============================\n');

	const board = createTestBoard();
	const K = 50; // Test with K=50

	// Load dictionary (copied from bestWords.ts)
	const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
	const dictBytes = readFileSync(DICT_BIN);
	const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
	const textDec = new TextDecoder();

	// Letter scores
	const LETTER_SCORES: number[] = Array(26).fill(0);
	Object.assign(LETTER_SCORES, {
		0: 1, 1: 4, 2: 4, 3: 2, 4: 1, 5: 4, 6: 2, 7: 4, 8: 1, 9: 10,
		10: 5, 11: 1, 12: 4, 13: 2, 14: 1, 15: 3, 16: 10, 17: 1, 18: 1, 19: 1,
		20: 2, 21: 5, 22: 4, 23: 8, 24: 3, 25: 10
	});

	// Parse dictionary entries
	console.log('1️⃣  Parsing dictionary entries:');
	const ENTRIES = profile('Parse all entries', () => {
		let o = 0;
		const nEntries = dv.getUint32(o, true);
		o += 4;
		const out: any[] = new Array(nEntries);

		for (let idx = 0; idx < nEntries; idx++) {
			const wordBytes = dv.getUint16(o, true);
			o += 2;
			const score = dv.getUint16(o, true);
			o += 2;
			const wlen = dv.getUint8(o);
			o += 1;
			const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
			o += 26;

			out[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
			o += wordBytes;
		}
		return out;
	});
	console.log(`   Loaded ${ENTRIES.length} entries\n`);

	// Board summarization
	console.log('2️⃣  Board summarization:');
	const bag = profile('Summarize board', () => {
		const freq = new Uint8Array(26);
		const tiles: Tile[][] = Array.from({ length: 26 }, (_) => []);
		for (const row of board.tiles) {
			for (const t of row) {
				const idx = t.letter.charCodeAt(0) - 65;
				freq[idx]++;
				tiles[idx].push(t);
			}
		}
		return { freq, tiles };
	});
	console.log();

	// Test main loop with limited entries
	console.log('3️⃣  Testing main loop with different entry counts:');
	
	for (const maxEntries of [100, 1000, 10000, 50000]) {
		const entriesToTest = ENTRIES.slice(0, Math.min(maxEntries, ENTRIES.length));
		
		const result = profile(`Process ${entriesToTest.length} entries`, () => {
			const candid: Word[] = [];
			let worstScore = 0;
			let processedCount = 0;
			let skippedLength = 0;
			let skippedHistogram = 0;
			let hungarianCalls = 0;

			for (const meta of entriesToTest) {
				processedCount++;
				
				// 1) quick length reject
				if (meta.wordLen > 25) {
					skippedLength++;
					continue;
				}

				// 2) histogram subset check
				let ok = true;
				for (let i = 0; i < 26; i++) {
					if (meta.hist[i] > bag.freq[i]) {
						ok = false;
						break;
					}
				}
				if (!ok) {
					skippedHistogram++;
					continue;
				}

				// 3) decode UTF-8 only now (lazy)
				const word = textDec.decode(
					new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
				);

				// 4) compute best placement score (simplified - skip Hungarian for now)
				hungarianCalls++;
				const score = meta.letterScoreSum; // Use base score for now
				
				if (score < worstScore) continue;

				// 5) keep in min-heap
				const dummyPositions: Array<[number, number]> = [];
				for (let i = 0; i < word.length; i++) {
					dummyPositions.push([0, i]);
				}
				const wordObj = new Word(word, dummyPositions, score);
				candid.push(wordObj);
				candid.sort((a, b) => b.score - a.score);
				if (candid.length > K) candid.pop();
				worstScore = candid.length === K ? candid[candid.length - 1].score : 0;
			}

			return {
				words: candid,
				stats: { processedCount, skippedLength, skippedHistogram, hungarianCalls }
			};
		});

		console.log(`   Found ${result.words.length} words`);
		console.log(`   Stats: processed=${result.stats.processedCount}, skipped_length=${result.stats.skippedLength}, skipped_histogram=${result.stats.skippedHistogram}, hungarian_calls=${result.stats.hungarianCalls}`);
	}
	console.log();

	// Test Hungarian algorithm performance
	console.log('4️⃣  Testing Hungarian algorithm performance:');
	for (const size of [3, 5, 8, 10, 12]) {
		const matrix = Array(size).fill(0).map(() => 
			Array(size).fill(0).map(() => Math.random() * 100)
		);
		
		profile(`Hungarian ${size}x${size}`, () => {
			for (let i = 0; i < 100; i++) {
				solveAssignment(matrix);
			}
		});
	}

	console.log('\n✅ bestWords profiling complete');
}

main().catch(console.error);
