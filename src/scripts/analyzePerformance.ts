/**
 * Performance analysis script for LettersBot solver
 * 
 * This script identifies performance bottlenecks by profiling individual components
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { bestWords } from '../lib/bestWords';
import { solveAssignment } from '../lib/solver/hungarian';
import { upperBoundForBoard } from '../lib/solver/optimization';
import { hashBoard } from '../lib/solver/hashing';

// Create a test board
function createTestBoard(): Board {
	const tiles = [
		['C', 'A', 'T', 'S', 'E'],
		['H', 'O', 'U', 'S', 'E'],
		['W', 'O', 'R', 'D', 'S'],
		['G', 'A', 'M', 'E', 'S'],
		['T', 'E', 'S', 'T', 'S']
	];

	const board = new Board();
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = new Tile(tiles[row][col], row, col);
			// Add some multipliers for realism
			if ((row + col) % 3 === 0) tile.letterMult = 2;
			if ((row + col) % 7 === 0) tile.wordMult = 2;
			board.setTile(row, col, tile);
		}
	}
	return board;
}

// Profile a function
function profile<T>(name: string, fn: () => T): T {
	const start = performance.now();
	const result = fn();
	const end = performance.now();
	console.log(`⏱️  ${name}: ${(end - start).toFixed(2)}ms`);
	return result;
}

// Profile an async function
async function profileAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
	const start = performance.now();
	const result = await fn();
	const end = performance.now();
	console.log(`⏱️  ${name}: ${(end - start).toFixed(2)}ms`);
	return result;
}

async function main() {
	console.log('🔍 LettersBot Performance Analysis');
	console.log('==================================\n');

	const board = createTestBoard();
	console.log('📋 Test board created\n');

	// Test 1: bestWords function with different K values
	console.log('1️⃣  Testing bestWords function:');
	for (const K of [10, 50, 100, 200]) {
		try {
			const words = profile(`bestWords(K=${K})`, () => bestWords(board, K));
			console.log(`   Found ${words.length} words`);
		} catch (error) {
			console.log(`   Error with K=${K}: ${error}`);
		}
	}
	console.log();

	// Test 2: Hungarian algorithm with different matrix sizes
	console.log('2️⃣  Testing Hungarian algorithm:');
	for (const size of [3, 5, 8, 10, 12]) {
		const matrix = Array(size).fill(0).map(() => 
			Array(size).fill(0).map(() => Math.random() * 100)
		);
		try {
			const result = profile(`Hungarian(${size}x${size})`, () => solveAssignment(matrix));
			console.log(`   Success: ${result.success}, Cost: ${result.cost.toFixed(2)}`);
		} catch (error) {
			console.log(`   Error with size ${size}: ${error}`);
		}
	}
	console.log();

	// Test 3: Board operations
	console.log('3️⃣  Testing board operations:');
	profile('hashBoard', () => {
		for (let i = 0; i < 1000; i++) {
			hashBoard(board);
		}
	});

	profile('upperBoundForBoard', () => {
		for (let i = 0; i < 100; i++) {
			upperBoundForBoard(board, { maxWordsPerPosition: 50 });
		}
	});
	console.log();

	// Test 4: Memory usage analysis
	console.log('4️⃣  Memory usage analysis:');
	const memBefore = process.memoryUsage();
	console.log(`   Memory before: ${(memBefore.heapUsed / 1024 / 1024).toFixed(2)} MB`);

	// Create many boards to test memory usage
	const boards = [];
	for (let i = 0; i < 100; i++) {
		boards.push(createTestBoard());
	}

	const memAfter = process.memoryUsage();
	console.log(`   Memory after creating 100 boards: ${(memAfter.heapUsed / 1024 / 1024).toFixed(2)} MB`);
	console.log(`   Memory increase: ${((memAfter.heapUsed - memBefore.heapUsed) / 1024 / 1024).toFixed(2)} MB`);
	console.log();

	// Test 5: Dictionary loading time (if applicable)
	console.log('5️⃣  Dictionary operations:');
	try {
		// Test multiple calls to see if there's caching
		profile('bestWords call 1', () => bestWords(board, 10));
		profile('bestWords call 2', () => bestWords(board, 10));
		profile('bestWords call 3', () => bestWords(board, 10));
	} catch (error) {
		console.log(`   Error testing dictionary: ${error}`);
	}

	console.log('\n✅ Performance analysis complete');
}

main().catch(console.error);
