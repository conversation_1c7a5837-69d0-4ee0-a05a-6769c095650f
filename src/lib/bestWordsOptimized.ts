/**
 * Optimized version of bestWords function
 * 
 * Key optimizations:
 * 1. Early termination using upper bound estimates
 * 2. Reduced Hungarian algorithm calls
 * 3. Better candidate management
 * 4. Cached computations
 */

import { readFileSync } from 'node:fs';
import { Tile } from './models/Tile';
import { Board } from './models/Board';
import { Word } from './models/Word';
import { solveAssignment } from './solver/hungarian';
import { join } from 'path';

const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
const dictBytes = readFileSync(DICT_BIN);
const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
const textDec = new TextDecoder();

// Letter scores
const LETTER_SCORES: number[] = Array(26).fill(0);
Object.assign(LETTER_SCORES, {
	0: 1, 1: 4, 2: 4, 3: 2, 4: 1, 5: 4, 6: 2, 7: 4, 8: 1, 9: 10,
	10: 5, 11: 1, 12: 4, 13: 2, 14: 1, 15: 3, 16: 10, 17: 1, 18: 1, 19: 1,
	20: 2, 21: 5, 22: 4, 23: 8, 24: 3, 25: 10
});

// Dictionary entries (loaded once)
interface DictEntryMeta {
	off: number;
	len: number;
	letterScoreSum: number;
	wordLen: number;
	hist: Uint8Array;
	upperBoundScore?: number; // Cached upper bound
}

const ENTRIES: DictEntryMeta[] = (() => {
	let o = 0;
	const nEntries = dv.getUint32(o, true);
	o += 4;
	const out: DictEntryMeta[] = new Array(nEntries);

	for (let idx = 0; idx < nEntries; idx++) {
		const wordBytes = dv.getUint16(o, true);
		o += 2;
		const score = dv.getUint16(o, true);
		o += 2;
		const wlen = dv.getUint8(o);
		o += 1;
		const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
		o += 26;

		out[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
		o += wordBytes;
	}
	return out;
})();

// Board summary interface
interface LetterBag {
	freq: Uint8Array;
	tiles: Tile[][];
	maxMultipliers: { letter: number; word: number }; // Cache max multipliers
}

function summariseBoard(board: Board): LetterBag {
	const freq = new Uint8Array(26);
	const tiles: Tile[][] = Array.from({ length: 26 }, (_) => []);
	let maxLetterMult = 1;
	let maxWordMult = 1;

	for (const row of board.tiles) {
		for (const t of row) {
			const idx = t.letter.charCodeAt(0) - 65;
			freq[idx]++;
			tiles[idx].push(t);
			maxLetterMult = Math.max(maxLetterMult, t.letterMult);
			maxWordMult = Math.max(maxWordMult, t.wordMult);
		}
	}

	return { 
		freq, 
		tiles, 
		maxMultipliers: { letter: maxLetterMult, word: maxWordMult }
	};
}

// Fast upper bound estimate (without Hungarian algorithm)
function estimateUpperBound(word: string, bag: LetterBag, letterScoreSum: number): number {
	// Optimistic estimate: assume best possible multipliers
	const maxPossibleScore = letterScoreSum * bag.maxMultipliers.letter * bag.maxMultipliers.word;
	return maxPossibleScore;
}

// Simplified placement scoring (faster than Hungarian for early filtering)
function fastPlacementScore(word: string, bag: LetterBag, letterScoreSum: number): number {
	let totalScore = 0;
	let wordMultiplier = 1;
	const usedTiles = new Set<string>();

	for (let i = 0; i < word.length; i++) {
		const ch = word[i];
		const letterIdx = ch.charCodeAt(0) - 65;
		const availableTiles = bag.tiles[letterIdx];
		
		// Find best available tile for this letter
		let bestTile = null;
		let bestScore = 0;
		
		for (const tile of availableTiles) {
			const tileKey = `${tile.row},${tile.col}`;
			if (usedTiles.has(tileKey)) continue;
			
			const letterScore = LETTER_SCORES[letterIdx] * tile.letterMult;
			if (letterScore > bestScore) {
				bestScore = letterScore;
				bestTile = tile;
			}
		}
		
		if (bestTile) {
			totalScore += bestScore;
			wordMultiplier *= bestTile.wordMult;
			usedTiles.add(`${bestTile.row},${bestTile.col}`);
		} else {
			// Fallback to base score
			totalScore += LETTER_SCORES[letterIdx];
		}
	}
	
	return totalScore * wordMultiplier;
}

// Precise Hungarian-based scoring (only for final candidates)
function precisePlacementScore(word: string, tilesBag: Tile[][], letterScoreSum: number): number {
	const n = word.length;
	const M = Array.from<number[], number>({ length: n }, () => new Array(n).fill(0));

	const letterCounts: Record<string, number> = {};
	for (let i = 0; i < n; i++) {
		const ch = word[i];
		letterCounts[ch] = (letterCounts[ch] ?? 0) + 1;
		const tileList = tilesBag[ch.charCodeAt(0) - 65];
		const startIdx = letterCounts[ch] - 1;
		for (let j = 0; j < n; j++) M[i][j] = 999999;
		
		let col = 0;
		for (let tIdx = startIdx; tIdx < tileList.length; tIdx++) {
			const tile = tileList[tIdx];
			const letterValue = LETTER_SCORES[ch.charCodeAt(0) - 65] * tile.letterMult;
			const wordMult = tile.wordMult;
			M[i][col++] = -letterValue - (wordMult > 1 ? 1e4 * wordMult : 0);
		}
	}

	const result = solveAssignment(M);
	if (!result.success || result.assignment.length !== n) {
		return letterScoreSum;
	}

	let letterSum = 0, wordMultTotal = 1;
	for (let r = 0; r < n; r++) {
		const letterIdx = word[r].charCodeAt(0) - 65;
		const tileList = tilesBag[letterIdx];
		const assignedCol = result.assignment[r];

		if (assignedCol < 0 || assignedCol >= tileList.length) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		const tile = tileList[assignedCol];
		if (!tile) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		letterSum += LETTER_SCORES[letterIdx] * tile.letterMult;
		wordMultTotal *= tile.wordMult;
	}
	return letterSum * wordMultTotal;
}

// Min-heap implementation for efficient candidate management
class MinHeap<T> {
	private items: T[] = [];
	private compare: (a: T, b: T) => number;

	constructor(compareFn: (a: T, b: T) => number) {
		this.compare = compareFn;
	}

	push(item: T): void {
		this.items.push(item);
		this.heapifyUp(this.items.length - 1);
	}

	pop(): T | undefined {
		if (this.items.length === 0) return undefined;
		if (this.items.length === 1) return this.items.pop();

		const root = this.items[0];
		this.items[0] = this.items.pop()!;
		this.heapifyDown(0);
		return root;
	}

	peek(): T | undefined {
		return this.items[0];
	}

	size(): number {
		return this.items.length;
	}

	toArray(): T[] {
		return [...this.items].sort(this.compare);
	}

	private heapifyUp(index: number): void {
		while (index > 0) {
			const parentIndex = Math.floor((index - 1) / 2);
			if (this.compare(this.items[index], this.items[parentIndex]) >= 0) break;
			[this.items[index], this.items[parentIndex]] = [this.items[parentIndex], this.items[index]];
			index = parentIndex;
		}
	}

	private heapifyDown(index: number): void {
		while (true) {
			let smallest = index;
			const leftChild = 2 * index + 1;
			const rightChild = 2 * index + 2;

			if (leftChild < this.items.length && this.compare(this.items[leftChild], this.items[smallest]) < 0) {
				smallest = leftChild;
			}
			if (rightChild < this.items.length && this.compare(this.items[rightChild], this.items[smallest]) < 0) {
				smallest = rightChild;
			}
			if (smallest === index) break;

			[this.items[index], this.items[smallest]] = [this.items[smallest], this.items[index]];
			index = smallest;
		}
	}
}

// Optimized main function
export function bestWordsOptimized(board: Board, K: number): Word[] {
	const bag = summariseBoard(board);
	const freqB = bag.freq;

	// Use min-heap for efficient candidate management
	const candidates = new MinHeap<{ word: Word; score: number }>((a, b) => a.score - b.score);
	let worstScore = 0;

	// Phase 1: Fast filtering with upper bound estimates
	const potentialCandidates: { meta: DictEntryMeta; word: string; fastScore: number }[] = [];

	for (const meta of ENTRIES) {
		// Quick length reject
		if (meta.wordLen > 25) continue;

		// Histogram subset check
		let ok = true;
		for (let i = 0; i < 26; i++) {
			if (meta.hist[i] > freqB[i]) {
				ok = false;
				break;
			}
		}
		if (!ok) continue;

		// Decode word
		const word = textDec.decode(
			new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
		);

		// Fast upper bound check
		const upperBound = estimateUpperBound(word, bag, meta.letterScoreSum);
		if (upperBound < worstScore) continue;

		// Fast placement score
		const fastScore = fastPlacementScore(word, bag, meta.letterScoreSum);
		if (fastScore < worstScore) continue;

		potentialCandidates.push({ meta, word, fastScore });
	}

	// Sort potential candidates by fast score (descending)
	potentialCandidates.sort((a, b) => b.fastScore - a.fastScore);

	// Phase 2: Precise scoring for top candidates only
	const maxPreciseEvaluations = Math.min(K * 3, potentialCandidates.length); // Evaluate 3x more than needed

	for (let i = 0; i < maxPreciseEvaluations; i++) {
		const candidate = potentialCandidates[i];
		
		// Precise Hungarian-based scoring
		const preciseScore = precisePlacementScore(candidate.word, bag.tiles, candidate.meta.letterScoreSum);
		
		if (preciseScore >= worstScore) {
			const dummyPositions: Array<[number, number]> = [];
			for (let j = 0; j < candidate.word.length; j++) {
				dummyPositions.push([0, j]);
			}
			const wordObj = new Word(candidate.word, dummyPositions, preciseScore);
			
			candidates.push({ word: wordObj, score: preciseScore });
			
			if (candidates.size() > K) {
				candidates.pop(); // Remove worst
			}
			
			if (candidates.size() === K) {
				worstScore = candidates.peek()?.score || 0;
			}
		}
	}

	// Convert to final result
	const result = candidates.toArray().map(c => c.word);
	result.sort((a, b) => b.score - a.score);
	return result;
}
