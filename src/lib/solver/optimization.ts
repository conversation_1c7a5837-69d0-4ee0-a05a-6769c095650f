/**
 * Optimization utilities for the solver algorithm
 * 
 * This module provides upper bound calculations and other optimization
 * techniques for efficient branch-and-bound search.
 */

import type { Board } from '../models/Board';
import type { GameState } from '../models/GameState';
import { GAME_CONFIG, LETTER_SCORES } from '../types';
import { getDictionary } from '../utils/dictionary';

/**
 * Configuration for upper bound calculations
 */
interface UpperBoundConfig {
	/** Consider letter multipliers in calculation */
	includeLetterMultipliers: boolean;
	/** Consider word multipliers in calculation */
	includeWordMultipliers: boolean;
	/** Maximum word length to consider */
	maxWordLength: number;
	/** Use optimistic tile placement */
	optimisticPlacement: boolean;
	/** Cache results for performance */
	useCache: boolean;
}

/**
 * Default upper bound configuration
 */
const DEFAULT_CONFIG: UpperBoundConfig = {
	includeLetterMultipliers: true,
	includeWordMultipliers: true,
	maxWordLength: 8,
	optimisticPlacement: true,
	useCache: true
};

/**
 * Cache for upper bound calculations
 */
const upperBoundCache = new Map<string, number>();

/**
 * Calculate upper bound score for a board state
 * 
 * This provides an optimistic estimate of the maximum possible score
 * that can be achieved from the current board state.
 */
export function upperBoundForBoard(
	board: Board,
	remainingTurns: number,
	config: UpperBoundConfig = DEFAULT_CONFIG
): number {
	// Generate cache key
	const cacheKey = config.useCache ? generateCacheKey(board, remainingTurns, config) : null;
	
	if (cacheKey && upperBoundCache.has(cacheKey)) {
		return upperBoundCache.get(cacheKey)!;
	}
	
	let upperBound = 0;
	
	if (remainingTurns <= 0) {
		return 0;
	}
	
	// Strategy 1: Optimistic single-turn bound
	const singleTurnBound = calculateSingleTurnUpperBound(board, config);
	
	// Strategy 2: Multi-turn projection
	const multiTurnBound = calculateMultiTurnUpperBound(board, remainingTurns, config);
	
	// Strategy 3: Letter frequency analysis
	const frequencyBound = calculateFrequencyBasedBound(board, remainingTurns, config);
	
	// Take the minimum of all bounds (most conservative)
	upperBound = Math.min(singleTurnBound * remainingTurns, multiTurnBound, frequencyBound);
	
	// Cache the result
	if (cacheKey) {
		upperBoundCache.set(cacheKey, upperBound);
	}
	
	return upperBound;
}

/**
 * Calculate upper bound for a single turn
 */
function calculateSingleTurnUpperBound(board: Board, config: UpperBoundConfig): number {
	const tiles = board.getAllTiles();
	
	// Find the highest value tiles
	const tileValues = tiles.map(tile => {
		let value = LETTER_SCORES[tile.letter] || 0;
		
		if (config.includeLetterMultipliers) {
			value *= tile.letterMult;
		}
		
		return {
			tile,
			value,
			wordMult: config.includeWordMultipliers ? tile.wordMult : 1
		};
	});
	
	// Sort by value descending
	tileValues.sort((a, b) => b.value - a.value);
	
	// Take the top tiles up to max word length
	const topTiles = tileValues.slice(0, Math.min(config.maxWordLength, tiles.length));
	
	// Calculate optimistic score
	let score = topTiles.reduce((sum, { value }) => sum + value, 0);
	
	// Apply maximum possible word multiplier
	if (config.includeWordMultipliers) {
		const maxWordMult = Math.max(...topTiles.map(({ wordMult }) => wordMult));
		score *= maxWordMult;
	}
	
	// Add length bonus for long words
	if (topTiles.length >= 7) {
		score += 50;
	} else if (topTiles.length >= 6) {
		score += 25;
	} else if (topTiles.length >= 5) {
		score += 10;
	}
	
	return score;
}

/**
 * Calculate upper bound considering multiple turns
 */
function calculateMultiTurnUpperBound(
	board: Board,
	remainingTurns: number,
	config: UpperBoundConfig
): number {
	let totalBound = 0;
	let currentBoard = board;
	
	for (let turn = 0; turn < remainingTurns; turn++) {
		// Calculate bound for this turn
		const turnBound = calculateSingleTurnUpperBound(currentBoard, config);
		totalBound += turnBound;
		
		// Simulate optimistic board evolution
		if (config.optimisticPlacement && turn < remainingTurns - 1) {
			currentBoard = simulateOptimisticBoardEvolution(currentBoard);
		}
	}
	
	return totalBound;
}

/**
 * Calculate upper bound based on letter frequency analysis
 */
function calculateFrequencyBasedBound(
	board: Board,
	remainingTurns: number,
	config: UpperBoundConfig
): number {
	const tiles = board.getAllTiles();
	
	// Count letter frequencies
	const letterCounts = new Map<string, number>();
	for (const tile of tiles) {
		letterCounts.set(tile.letter, (letterCounts.get(tile.letter) || 0) + 1);
	}
	
	// Calculate theoretical maximum based on common word patterns
	let maxScore = 0;
	
	try {
		const dictionary = getDictionary();
		
		// Find high-scoring words that could theoretically be formed
		const possibleWords = dictionary.findPossibleWords(
			tiles.map(t => t.letter),
			config.maxWordLength
		);
		
		// Sort by base score
		possibleWords.sort((a, b) => b.baseScore - a.baseScore);
		
		// Take top words for each turn
		for (let turn = 0; turn < remainingTurns && turn < possibleWords.length; turn++) {
			const word = possibleWords[turn];
			let score = word.baseScore;
			
			// Apply optimistic multipliers
			if (config.includeLetterMultipliers) {
				score *= 2; // Assume average 2x letter multiplier
			}
			
			if (config.includeWordMultipliers) {
				score *= 2; // Assume average 2x word multiplier
			}
			
			maxScore += score;
		}
	} catch (error) {
		// Fallback if dictionary not available
		const avgLetterValue = tiles.reduce((sum, tile) => sum + (LETTER_SCORES[tile.letter] || 0), 0) / tiles.length;
		const avgWordLength = 5;
		const avgMultiplier = config.includeLetterMultipliers || config.includeWordMultipliers ? 2 : 1;
		
		maxScore = remainingTurns * avgLetterValue * avgWordLength * avgMultiplier;
	}
	
	return maxScore;
}

/**
 * Simulate optimistic board evolution after a move
 */
function simulateOptimisticBoardEvolution(board: Board): Board {
	// For simplicity, assume the board gets better tiles
	// In a real implementation, this could be more sophisticated
	
	const newBoard = board.clone();
	const tiles = newBoard.getAllTiles();
	
	// Replace some low-value tiles with high-value ones
	const lowValueTiles = tiles.filter(tile => (LETTER_SCORES[tile.letter] || 0) <= 2);
	const highValueLetters = ['Q', 'X', 'Z', 'J', 'K'];
	
	for (let i = 0; i < Math.min(3, lowValueTiles.length); i++) {
		const tile = lowValueTiles[i];
		const newLetter = highValueLetters[i % highValueLetters.length];
		const newTile = tile.withLetter(newLetter);
		newBoard.setTile(tile.row, tile.col, newTile);
	}
	
	return newBoard;
}

/**
 * Generate cache key for upper bound calculation
 */
function generateCacheKey(board: Board, remainingTurns: number, config: UpperBoundConfig): string {
	const tiles = board.getAllTiles();
	const tileString = tiles.map(tile => 
		`${tile.letter}${tile.letterMult}${tile.wordMult}`
	).join('');
	
	const configString = `${config.includeLetterMultipliers}${config.includeWordMultipliers}${config.maxWordLength}`;
	
	return `${tileString}:${remainingTurns}:${configString}`;
}

/**
 * Calculate upper bound for a game state
 */
export function upperBoundForGameState(
	gameState: GameState,
	config: UpperBoundConfig = DEFAULT_CONFIG
): number {
	const remainingTurns = gameState.getRemainingTurns();
	return upperBoundForBoard(gameState.board, remainingTurns, config);
}

/**
 * Check if a node should be pruned based on upper bound
 */
export function shouldPruneNode(
	gameState: GameState,
	currentBestScore: number,
	config: UpperBoundConfig = DEFAULT_CONFIG
): boolean {
	const upperBound = upperBoundForGameState(gameState, config);
	const potentialTotal = gameState.total + upperBound;
	
	return potentialTotal <= currentBestScore;
}

/**
 * Calculate tightened upper bound using more sophisticated analysis
 */
export function calculateTightenedUpperBound(
	board: Board,
	remainingTurns: number,
	config: UpperBoundConfig = DEFAULT_CONFIG
): number {
	// This could use the Hungarian algorithm for optimal assignment
	// For now, use a simplified approach
	
	const basicBound = upperBoundForBoard(board, remainingTurns, config);
	
	// Apply tightening factors
	const connectivityFactor = calculateConnectivityFactor(board);
	const diversityFactor = calculateLetterDiversityFactor(board);
	
	return basicBound * connectivityFactor * diversityFactor;
}

/**
 * Calculate how well-connected the board tiles are
 */
function calculateConnectivityFactor(board: Board): number {
	const tiles = board.getAllTiles();
	let totalConnections = 0;
	let maxPossibleConnections = 0;
	
	for (const tile of tiles) {
		const adjacent = board.getAdjacentTiles(tile.row, tile.col);
		totalConnections += adjacent.length;
		maxPossibleConnections += 8; // Maximum 8 adjacent tiles
	}
	
	return totalConnections / maxPossibleConnections;
}

/**
 * Calculate letter diversity factor
 */
function calculateLetterDiversityFactor(board: Board): number {
	const tiles = board.getAllTiles();
	const uniqueLetters = new Set(tiles.map(tile => tile.letter));
	
	// More diverse boards have more word formation potential
	return Math.min(1.0, uniqueLetters.size / 20); // Normalize to 20 unique letters
}

/**
 * Clear the upper bound cache
 */
export function clearUpperBoundCache(): void {
	upperBoundCache.clear();
}

/**
 * Get cache statistics
 */
export function getUpperBoundCacheStats(): {
	size: number;
	hitRate: number;
} {
	// This would require tracking hits/misses in a real implementation
	return {
		size: upperBoundCache.size,
		hitRate: 0.0 // Placeholder
	};
}

/**
 * Benchmark upper bound calculation performance
 */
export function benchmarkUpperBoundCalculation(
	boards: Board[],
	remainingTurns: number = 3
): {
	averageTime: number;
	totalTime: number;
	calculations: number;
} {
	const startTime = performance.now();
	
	for (const board of boards) {
		upperBoundForBoard(board, remainingTurns);
	}
	
	const endTime = performance.now();
	const totalTime = endTime - startTime;
	const averageTime = totalTime / boards.length;
	
	return {
		averageTime,
		totalTime,
		calculations: boards.length
	};
}
