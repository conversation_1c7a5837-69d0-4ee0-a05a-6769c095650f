/**
 * Comprehensive test suite for bestWordsFast function
 *
 * Tests multiplier prioritization, performance, edge cases, and correctness
 */

import { Board } from '../models/Board';
import { Tile } from '../models/Tile';
import { Word } from '../models/Word';
import { bestWordsFast } from '../bestWordsFast';

interface TestResult {
	name: string;
	passed: boolean;
	error?: string;
	details?: any;
}

interface PerformanceMetrics {
	executionTime: number;
	wordsFound: number;
	averageScore: number;
	topScore: number;
}

/**
 * Create a test board with known multipliers for testing prioritization
 */
function createMultiplierTestBoard(): Board {
	const tiles: Tile[][] = [];

	// Board layout with strategic multipliers
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['A', 'R', 'E', 'A', 'T'],
		['T', 'E', 'S', 'T', 'S'],
		['S', 'A', 'T', 'E', 'R'],
		['E', 'T', 'S', 'R', 'A']
	];

	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			let letterMult: 1 | 2 | 3 = 1;
			let wordMult: 1 | 2 | 3 = 1;

			// Strategic multiplier placement
			if (row === 0 && col === 0) letterMult = 3; // C with 3x letter
			if (row === 1 && col === 0) wordMult = 2; // A with 2x word
			if (row === 0 && col === 2) letterMult = 2; // T with 2x letter
			if (row === 2 && col === 2) wordMult = 3; // S with 3x word
			if (row === 4 && col === 4) letterMult = 2; // A with 2x letter

			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				letterMult,
				wordMult
			);
		}
	}

	return new Board(tiles);
}

/**
 * Create a board with duplicate letters and different multipliers
 */
function createDuplicateLetterBoard(): Board {
	const tiles: Tile[][] = [];

	// Board with common letters that should form valid words like CAT, ATE, etc.
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['A', 'R', 'E', 'A', 'T'],
		['T', 'E', 'S', 'T', 'S'],
		['S', 'A', 'T', 'E', 'R'],
		['E', 'T', 'S', 'R', 'A']
	];

	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			let letterMult: 1 | 2 | 3 = 1;
			let wordMult: 1 | 2 | 3 = 1;

			// Give different multipliers to same letters
			if (letters[row][col] === 'A') {
				if (row === 0 && col === 1) letterMult = 3; // Best A at [0,1]
				if (row === 1 && col === 0) letterMult = 2; // Good A at [1,0]
				if (row === 1 && col === 3) wordMult = 2; // A with word mult at [1,3]
				// Other A's have no multipliers
			}

			if (letters[row][col] === 'T') {
				if (row === 0 && col === 2) wordMult = 3; // Best T at [0,2]
				if (row === 1 && col === 4) letterMult = 2; // Good T at [1,4]
				// Other T's have no multipliers
			}

			if (letters[row][col] === 'E') {
				if (row === 1 && col === 2) letterMult = 2; // E with multiplier at [1,2]
			}

			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				letterMult,
				wordMult
			);
		}
	}

	return new Board(tiles);
}

/**
 * Test multiplier prioritization
 */
function testMultiplierPrioritization(): TestResult {
	try {
		const board = createDuplicateLetterBoard();
		const words = bestWordsFast(board, 10);

		// Find words that use the letter 'A'
		const wordsWithA = words.filter((word) => word.letters.includes('A'));

		if (wordsWithA.length === 0) {
			return { name: 'Multiplier Prioritization', passed: false, error: 'No words with A found' };
		}

		// Check if the algorithm is using the best A tiles
		let usedBestATile = false;
		for (const word of wordsWithA) {
			for (const [row, col] of word.positions) {
				const tile = board.getTile(row, col);
				if (tile && tile.letter === 'A' && (tile.letterMult > 1 || tile.wordMult > 1)) {
					usedBestATile = true;
					break;
				}
			}
			if (usedBestATile) break;
		}

		return {
			name: 'Multiplier Prioritization',
			passed: usedBestATile,
			details: {
				wordsWithA: wordsWithA.length,
				topWord: words[0]?.letters,
				topScore: words[0]?.score
			}
		};
	} catch (error) {
		return { name: 'Multiplier Prioritization', passed: false, error: String(error) };
	}
}

/**
 * Test performance characteristics
 */
function testPerformance(): TestResult {
	try {
		const board = Board.createRandom();
		const testSizes = [10, 25, 50, 100];
		const metrics: PerformanceMetrics[] = [];

		for (const K of testSizes) {
			const startTime = performance.now();
			const words = bestWordsFast(board, K);
			const endTime = performance.now();

			const executionTime = endTime - startTime;
			const averageScore =
				words.length > 0 ? words.reduce((sum, w) => sum + w.score, 0) / words.length : 0;
			const topScore = words.length > 0 ? words[0].score : 0;

			metrics.push({
				executionTime,
				wordsFound: words.length,
				averageScore,
				topScore
			});

			// Performance should be reasonable (under 1000ms for K=100)
			if (K === 100 && executionTime > 1000) {
				return {
					name: 'Performance',
					passed: false,
					error: `Too slow: ${executionTime}ms for K=100`,
					details: metrics
				};
			}
		}

		return {
			name: 'Performance',
			passed: true,
			details: metrics
		};
	} catch (error) {
		return { name: 'Performance', passed: false, error: String(error) };
	}
}

/**
 * Test edge cases
 */
function testEdgeCases(): TestResult {
	try {
		const board = Board.createRandom();

		// Test K=0
		const words0 = bestWordsFast(board, 0);
		if (words0.length !== 0) {
			return { name: 'Edge Cases', passed: false, error: 'K=0 should return empty array' };
		}

		// Test K=1
		const words1 = bestWordsFast(board, 1);
		if (words1.length > 1) {
			return { name: 'Edge Cases', passed: false, error: 'K=1 should return at most 1 word' };
		}

		// Test large K
		const words1000 = bestWordsFast(board, 1000);
		// Should not crash and should return reasonable number of words

		// Safe access to scores
		let k1TopScore = 0;
		let k1000TopScore = 0;

		try {
			k1TopScore = words1.length > 0 && words1[0] ? words1[0].score : 0;
		} catch (e) {
			console.log('Error accessing words1[0].score:', e, 'words1:', words1);
		}

		try {
			k1000TopScore = words1000.length > 0 && words1000[0] ? words1000[0].score : 0;
		} catch (e) {
			console.log('Error accessing words1000[0].score:', e, 'words1000:', words1000);
		}

		return {
			name: 'Edge Cases',
			passed: true,
			details: {
				k0Results: words0.length,
				k1Results: words1.length,
				k1000Results: words1000.length,
				k1TopScore,
				k1000TopScore
			}
		};
	} catch (error) {
		return { name: 'Edge Cases', passed: false, error: String(error) };
	}
}

/**
 * Test word validity and scoring consistency
 */
function testWordValidity(): TestResult {
	try {
		const board = createMultiplierTestBoard();
		const words = bestWordsFast(board, 20);

		for (const word of words) {
			// Check word length
			if (word.letters.length !== word.positions.length) {
				return {
					name: 'Word Validity',
					passed: false,
					error: `Word ${word.letters} has mismatched letters/positions length`
				};
			}

			// Check positions are valid
			for (const [row, col] of word.positions) {
				if (row < 0 || row >= 5 || col < 0 || col >= 5) {
					return {
						name: 'Word Validity',
						passed: false,
						error: `Word ${word.letters} has invalid position [${row},${col}]`
					};
				}

				const tile = board.getTile(row, col);
				if (!tile) {
					return {
						name: 'Word Validity',
						passed: false,
						error: `Word ${word.letters} references null tile at [${row},${col}]`
					};
				}
			}

			// Check score is positive
			if (word.score <= 0) {
				return {
					name: 'Word Validity',
					passed: false,
					error: `Word ${word.letters} has non-positive score: ${word.score}`
				};
			}
		}

		return {
			name: 'Word Validity',
			passed: true,
			details: { wordsChecked: words.length }
		};
	} catch (error) {
		return { name: 'Word Validity', passed: false, error: String(error) };
	}
}

/**
 * Run all tests
 */
export function runBestWordsFastTests(): TestResult[] {
	console.log('🧪 Running bestWordsFast comprehensive test suite...\n');

	const tests = [testMultiplierPrioritization, testPerformance, testEdgeCases, testWordValidity];

	const results: TestResult[] = [];

	for (const test of tests) {
		console.log(`Running ${test.name}...`);
		const result = test();
		results.push(result);

		if (result.passed) {
			console.log(`✅ ${result.name} passed`);
		} else {
			console.log(`❌ ${result.name} failed: ${result.error}`);
		}

		if (result.details) {
			console.log(`   Details:`, result.details);
		}
		console.log();
	}

	const passedCount = results.filter((r) => r.passed).length;
	console.log(`\n📊 Test Results: ${passedCount}/${results.length} tests passed`);

	return results;
}

// Export test utilities for external use
export {
	createMultiplierTestBoard,
	createDuplicateLetterBoard,
	testMultiplierPrioritization,
	testPerformance,
	testEdgeCases,
	testWordValidity
};
