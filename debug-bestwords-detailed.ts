/**
 * Detailed debug script for bestWordsFast function
 */

import { Board } from './src/lib/models/Board';
import { Tile } from './src/lib/models/Tile';
import { readFileSync } from 'node:fs';
import { join } from 'path';

// Create a simple test board
function createSimpleBoard(): Board {
	const tiles: Tile[][] = [];
	
	// Simple board with common letters
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['A', 'R', 'E', 'A', 'T'],
		['T', 'E', 'S', 'T', 'S'],
		['S', 'A', 'T', 'E', 'R'],
		['E', 'T', 'S', 'R', 'A']
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1, // No multipliers for simplicity
				1
			);
		}
	}
	
	return new Board(tiles);
}

// Simplified version of the bestWordsFast logic for debugging
function debugBestWordsFast() {
	console.log('🔍 Debugging bestWordsFast internals...\n');
	
	const board = createSimpleBoard();
	
	// Check if dictionary loads
	console.log('1. Loading dictionary...');
	try {
		const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
		const dictBytes = readFileSync(DICT_BIN);
		const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
		const textDec = new TextDecoder();
		
		let o = 0;
		const nEntries = dv.getUint32(o, true);
		console.log(`   Dictionary has ${nEntries} entries`);
		
		// Check first few entries
		o += 4;
		for (let i = 0; i < Math.min(5, nEntries); i++) {
			const wordBytes = dv.getUint16(o, true);
			o += 2;
			const score = dv.getUint16(o, true);
			o += 2;
			const wlen = dv.getUint8(o);
			o += 1;
			const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
			o += 26;
			
			const word = textDec.decode(
				new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, wordBytes)
			);
			o += wordBytes;
			
			console.log(`   Entry ${i}: "${word}" (length: ${wlen}, score: ${score})`);
		}
	} catch (error) {
		console.error('   ❌ Error loading dictionary:', error);
		return;
	}
	
	// Check board summary
	console.log('\n2. Board summary...');
	const freq = new Uint8Array(26);
	for (const row of board.tiles) {
		for (const t of row) {
			const idx = t.letter.charCodeAt(0) - 65;
			freq[idx]++;
		}
	}
	
	console.log('   Letter frequencies:');
	for (let i = 0; i < 26; i++) {
		if (freq[i] > 0) {
			const letter = String.fromCharCode(65 + i);
			console.log(`   ${letter}: ${freq[i]}`);
		}
	}
	
	// Check if simple words like "CAT" should be possible
	console.log('\n3. Checking simple words...');
	const testWords = ['CAT', 'ATE', 'EAT', 'TEA', 'SET', 'SAT', 'TAR', 'ART'];
	
	for (const word of testWords) {
		console.log(`   Checking "${word}":`);
		
		// Check if letters are available
		const wordFreq = new Uint8Array(26);
		for (const char of word) {
			const idx = char.charCodeAt(0) - 65;
			wordFreq[idx]++;
		}
		
		let hasLetters = true;
		for (let i = 0; i < 26; i++) {
			if (wordFreq[i] > freq[i]) {
				hasLetters = false;
				const letter = String.fromCharCode(65 + i);
				console.log(`     ❌ Not enough ${letter}: need ${wordFreq[i]}, have ${freq[i]}`);
				break;
			}
		}
		
		if (hasLetters) {
			console.log(`     ✅ Letters available`);
		}
	}
}

debugBestWordsFast();
