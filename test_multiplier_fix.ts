/**
 * Test script to verify that bestWordsFast properly prioritizes multipliers
 */

import { Board } from './src/lib/models/Board';
import { Tile } from './src/lib/models/Tile';
import { bestWordsFast } from './src/lib/bestWordsFast';

// Create a test board with specific multipliers
function createTestBoard(): Board {
	const tiles: Tile[][] = [];
	
	// Create a 5x5 board with specific letters and multipliers
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['A', 'R', 'E', 'A', 'T'],
		['T', 'E', 'S', 'T', 'S'],
		['S', 'A', 'T', 'E', 'R'],
		['E', 'T', 'S', 'R', 'A']
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			let letterMult: 1 | 2 | 3 = 1;
			let wordMult: 1 | 2 | 3 = 1;
			
			// Add multipliers to specific positions
			// Position [0,0] has 'C' with triple letter multiplier
			if (row === 0 && col === 0) {
				letterMult = 3;
			}
			// Position [1,0] has 'A' with double word multiplier  
			if (row === 1 && col === 0) {
				wordMult = 2;
			}
			// Position [2,0] has 'T' with no multipliers
			// Position [0,2] has 'T' with double letter multiplier
			if (row === 0 && col === 2) {
				letterMult = 2;
			}
			
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				letterMult,
				wordMult
			);
		}
	}
	
	return new Board(tiles);
}

// Test the function
function testMultiplierPrioritization() {
	const board = createTestBoard();
	
	console.log('Board layout:');
	for (let row = 0; row < 5; row++) {
		let rowStr = '';
		for (let col = 0; col < 5; col++) {
			const tile = board.getTile(row, col);
			if (tile) {
				const mult = tile.letterMult > 1 || tile.wordMult > 1 
					? `(L${tile.letterMult}×W${tile.wordMult}×)` 
					: '';
				rowStr += `${tile.letter}${mult}`.padEnd(8);
			}
		}
		console.log(rowStr);
	}
	
	console.log('\nFinding best words...');
	const words = bestWordsFast(board, 5);
	
	console.log('\nTop words found:');
	words.forEach((word, index) => {
		console.log(`${index + 1}. ${word.letters} - Score: ${word.score}`);
		console.log(`   Positions: ${word.positions.map(([r, c]) => `[${r},${c}]`).join(', ')}`);
		
		// Show which tiles were selected
		const tilesUsed = word.positions.map(([r, c]) => {
			const tile = board.getTile(r, c);
			return tile ? `${tile.letter}(L${tile.letterMult}×W${tile.wordMult}×)` : 'null';
		});
		console.log(`   Tiles: ${tilesUsed.join(', ')}`);
		console.log('');
	});
}

// Run the test
testMultiplierPrioritization();
