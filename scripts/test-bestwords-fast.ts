#!/usr/bin/env tsx

/**
 * Test runner for bestWordsFast function
 */

import { runBestWordsFastTests } from '../src/lib/tests/bestWordsFast.test';

async function main() {
	console.log('🚀 Starting bestWordsFast Test Suite');
	console.log('=====================================\n');
	
	try {
		const results = runBestWordsFastTests();
		
		const failedTests = results.filter(r => !r.passed);
		
		if (failedTests.length === 0) {
			console.log('🎉 All tests passed!');
			process.exit(0);
		} else {
			console.log('\n❌ Some tests failed:');
			failedTests.forEach(test => {
				console.log(`   - ${test.name}: ${test.error}`);
			});
			process.exit(1);
		}
	} catch (error) {
		console.error('💥 Test suite crashed:', error);
		process.exit(1);
	}
}

main();
