#!/usr/bin/env tsx

/**
 * Performance analysis script for bestWordsFast function
 */

import { Board } from '../src/lib/models/Board';
import { bestWordsFast } from '../src/lib/bestWordsFast';

interface PerformanceResult {
	K: number;
	executionTime: number;
	wordsFound: number;
	averageScore: number;
	topScore: number;
	memoryUsage?: NodeJS.MemoryUsage;
}

function measurePerformance<T>(name: string, fn: () => T): { result: T; time: number; memory: NodeJS.MemoryUsage } {
	// Force garbage collection if available
	if (global.gc) {
		global.gc();
	}
	
	const startMemory = process.memoryUsage();
	const startTime = performance.now();
	
	const result = fn();
	
	const endTime = performance.now();
	const endMemory = process.memoryUsage();
	
	const time = endTime - startTime;
	const memory = {
		rss: endMemory.rss - startMemory.rss,
		heapTotal: endMemory.heapTotal - startMemory.heapTotal,
		heapUsed: endMemory.heapUsed - startMemory.heapUsed,
		external: endMemory.external - startMemory.external,
		arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers
	};
	
	console.log(`⏱️  ${name}: ${time.toFixed(2)}ms`);
	
	return { result, time, memory };
}

function analyzePerformance() {
	console.log('📊 Performance Analysis for bestWordsFast');
	console.log('==========================================\n');
	
	// Test with different board types
	const testBoards = [
		{ name: 'Random Board 1', board: Board.createRandom() },
		{ name: 'Random Board 2', board: Board.createRandom() },
		{ name: 'Random Board 3', board: Board.createRandom() }
	];
	
	const testSizes = [5, 10, 25, 50, 100, 200];
	
	for (const { name, board } of testBoards) {
		console.log(`🎲 Testing with ${name}:`);
		console.log('Board preview:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row].map(t => 
				`${t.letter}${t.letterMult > 1 ? `(${t.letterMult}L)` : ''}${t.wordMult > 1 ? `(${t.wordMult}W)` : ''}`
			).join(' ');
			console.log(`   ${rowStr}`);
		}
		console.log();
		
		const results: PerformanceResult[] = [];
		
		for (const K of testSizes) {
			const measurement = measurePerformance(`bestWordsFast(K=${K})`, () => {
				return bestWordsFast(board, K);
			});
			
			const words = measurement.result;
			const averageScore = words.length > 0 ? words.reduce((sum, w) => sum + w.score, 0) / words.length : 0;
			const topScore = words.length > 0 ? words[0].score : 0;
			
			const result: PerformanceResult = {
				K,
				executionTime: measurement.time,
				wordsFound: words.length,
				averageScore: Math.round(averageScore * 100) / 100,
				topScore,
				memoryUsage: measurement.memory
			};
			
			results.push(result);
			
			console.log(`   K=${K}: ${words.length} words, top score: ${topScore}, avg: ${result.averageScore}`);
			
			// Show top 3 words for smaller K values
			if (K <= 10 && words.length > 0) {
				const topWords = words.slice(0, 3).map(w => `${w.letters}(${w.score})`).join(', ');
				console.log(`     Top words: ${topWords}`);
			}
		}
		
		console.log('\n   📈 Performance Summary:');
		console.log('   K\tTime(ms)\tWords\tTop Score\tAvg Score\tMemory(MB)');
		results.forEach(r => {
			const memoryMB = (r.memoryUsage!.heapUsed / 1024 / 1024).toFixed(2);
			console.log(`   ${r.K}\t${r.executionTime.toFixed(1)}\t\t${r.wordsFound}\t${r.topScore}\t\t${r.averageScore}\t\t${memoryMB}`);
		});
		
		// Performance analysis
		console.log('\n   🔍 Performance Analysis:');
		
		// Check if performance scales linearly with K
		const timeFor50 = results.find(r => r.K === 50)?.executionTime || 0;
		const timeFor100 = results.find(r => r.K === 100)?.executionTime || 0;
		const scalingRatio = timeFor100 / timeFor50;
		console.log(`     Scaling (K=100 vs K=50): ${scalingRatio.toFixed(2)}x`);
		
		// Check memory efficiency
		const maxMemory = Math.max(...results.map(r => r.memoryUsage!.heapUsed));
		console.log(`     Peak memory usage: ${(maxMemory / 1024 / 1024).toFixed(2)} MB`);
		
		// Check if we're finding enough words
		const wordsFoundAt100 = results.find(r => r.K === 100)?.wordsFound || 0;
		if (wordsFoundAt100 < 50) {
			console.log(`     ⚠️  Warning: Only found ${wordsFoundAt100} words for K=100, may indicate limited dictionary coverage`);
		}
		
		console.log('\n' + '─'.repeat(60) + '\n');
	}
	
	// Overall recommendations
	console.log('💡 Performance Recommendations:');
	console.log('1. For real-time use, K=25-50 provides good balance of speed and quality');
	console.log('2. For analysis, K=100-200 provides comprehensive results');
	console.log('3. Memory usage is reasonable across all K values');
	console.log('4. Consider caching dictionary loading for multiple calls');
}

// Run the analysis
analyzePerformance();
