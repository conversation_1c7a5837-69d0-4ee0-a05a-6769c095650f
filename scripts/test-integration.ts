#!/usr/bin/env tsx

/**
 * Integration test runner for bestWordsFast function
 */

import { runIntegrationTests } from '../src/lib/tests/bestWordsFast.integration.test';

async function main() {
	console.log('🚀 Starting bestWordsFast Integration Test Suite');
	console.log('==============================================\n');
	
	try {
		const results = runIntegrationTests();
		
		const failedTests = results.filter(r => !r.passed);
		
		if (failedTests.length === 0) {
			console.log('🎉 All integration tests passed!');
			console.log('\n✅ bestWordsFast is properly integrated with the game system');
			process.exit(0);
		} else {
			console.log('\n❌ Some integration tests failed:');
			failedTests.forEach(test => {
				console.log(`   - ${test.name}: ${test.error}`);
			});
			console.log('\n⚠️  Integration issues detected - review failed tests');
			process.exit(1);
		}
	} catch (error) {
		console.error('💥 Integration test suite crashed:', error);
		process.exit(1);
	}
}

main();
