/**
 * Debug script to check Word objects returned by bestWordsFast
 */

import { Board } from './src/lib/models/Board';
import { bestWordsFast } from './src/lib/bestWordsFast';

function debugWordObjects() {
	console.log('🔍 Debugging Word objects from bestWordsFast...\n');
	
	const board = Board.createRandom();
	
	console.log('Testing with K=5...');
	const words = bestWordsFast(board, 5);
	
	console.log(`Found ${words.length} words:`);
	
	words.forEach((word, index) => {
		console.log(`\nWord ${index + 1}:`);
		console.log(`  Type: ${typeof word}`);
		console.log(`  Constructor: ${word.constructor.name}`);
		console.log(`  Letters: ${word.letters} (type: ${typeof word.letters})`);
		console.log(`  Score: ${word.score} (type: ${typeof word.score})`);
		console.log(`  Positions: ${JSON.stringify(word.positions)} (type: ${typeof word.positions})`);
		
		// Check if score property exists
		console.log(`  Has score property: ${word.hasOwnProperty('score')}`);
		console.log(`  Score descriptor:`, Object.getOwnPropertyDescriptor(word, 'score'));
		
		// Try to access score safely
		try {
			const score = word.score;
			console.log(`  Score access successful: ${score}`);
		} catch (error) {
			console.log(`  Score access failed: ${error}`);
		}
		
		// Check all properties
		console.log(`  All properties:`, Object.getOwnPropertyNames(word));
	});
	
	// Test edge cases
	console.log('\n\nTesting edge cases...');
	
	try {
		const words0 = bestWordsFast(board, 0);
		console.log(`K=0: ${words0.length} words`);
	} catch (error) {
		console.log(`K=0 failed: ${error}`);
	}
	
	try {
		const words1 = bestWordsFast(board, 1);
		console.log(`K=1: ${words1.length} words`);
		if (words1.length > 0) {
			console.log(`  First word: ${words1[0]?.letters}, score: ${words1[0]?.score}`);
		}
	} catch (error) {
		console.log(`K=1 failed: ${error}`);
	}
}

debugWordObjects();
