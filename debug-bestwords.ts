/**
 * Debug script for bestWordsFast function
 */

import { Board } from './src/lib/models/Board';
import { Tile } from './src/lib/models/Tile';
import { bestWordsFast } from './src/lib/bestWordsFast';

// Create a simple test board
function createSimpleBoard(): Board {
	const tiles: Tile[][] = [];

	// Simple board with common letters
	const letters = [
		['C', 'A', 'T', 'S', 'E'],
		['A', 'R', 'E', 'A', 'T'],
		['T', 'E', 'S', 'T', 'S'],
		['S', 'A', 'T', 'E', 'R'],
		['E', 'T', 'S', 'R', 'A']
	];

	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1, // No multipliers for simplicity
				1
			);
		}
	}

	return new Board(tiles);
}

function debugBestWords() {
	console.log('🔍 Debugging bestWordsFast function...\n');

	const board = createSimpleBoard();

	console.log('Board layout:');
	for (let row = 0; row < 5; row++) {
		let rowStr = '';
		for (let col = 0; col < 5; col++) {
			const tile = board.getTile(row, col);
			if (tile) {
				rowStr += tile.letter + ' ';
			}
		}
		console.log(rowStr);
	}
	console.log();

	// Check letter frequencies
	console.log('Letter frequencies on board:');
	const letterCounts: Record<string, number> = {};
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = board.getTile(row, col);
			if (tile) {
				letterCounts[tile.letter] = (letterCounts[tile.letter] || 0) + 1;
			}
		}
	}
	console.log(letterCounts);
	console.log();

	console.log('Testing bestWordsFast with K=5...');
	try {
		const words = bestWordsFast(board, 5);
		console.log(`Found ${words.length} words:`);

		words.forEach((word, index) => {
			console.log(`${index + 1}. ${word.letters} - Score: ${word.score}`);
			console.log(`   Positions: ${word.positions.map(([r, c]) => `[${r},${c}]`).join(', ')}`);
		});

		if (words.length === 0) {
			console.log('\n❌ No words found! This suggests an issue with the function.');
			console.log('Expected words like: CAT, ATE, EAT, TEA, SET, etc.');
		}
	} catch (error) {
		console.error('❌ Error calling bestWordsFast:', error);
	}

	// Test with random board
	console.log('\n🎲 Testing with random board...');
	try {
		const randomBoard = Board.createRandom();
		const randomWords = bestWordsFast(randomBoard, 5);
		console.log(`Found ${randomWords.length} words on random board`);

		if (randomWords.length > 0) {
			console.log(`Top word: ${randomWords[0].letters} (${randomWords[0].score} points)`);
		}
	} catch (error) {
		console.error('❌ Error with random board:', error);
	}
}

debugBestWords();
